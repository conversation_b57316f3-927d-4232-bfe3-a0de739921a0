package com.business_clean.app.config;

/**
 * 配置状态枚举
 * 用于统一管理各种功能的配置状态
 * 
 * <AUTHOR> Assistant
 * @since 2025-08-08
 */
public enum ConfigState {
    
    /**
     * 1 - 默认开启，允许用户更改
     * 功能默认启用，用户可以在设置中开启或关闭
     */
    DEFAULT_ON_USER_CHANGEABLE(1, "默认开启，允许用户更改", true, true),
    
    /**
     * 2 - 默认关闭，允许用户更改  
     * 功能默认禁用，用户可以在设置中开启或关闭
     */
    DEFAULT_OFF_USER_CHANGEABLE(2, "默认关闭，允许用户更改", false, true),
    
    /**
     * 3 - 始终开启，用户无法更改
     * 功能强制启用，用户无法在设置中修改
     */
    ALWAYS_ON_USER_LOCKED(3, "始终开启，用户无法更改", true, false),
    
    /**
     * 4 - 始终关闭，用户无法更改
     * 功能强制禁用，用户无法在设置中修改
     */
    ALWAYS_OFF_USER_LOCKED(4, "始终关闭，用户无法更改", false, false);
    
    private final int code;
    private final String description;
    private final boolean defaultEnabled;
    private final boolean userChangeable;
    
    ConfigState(int code, String description, boolean defaultEnabled, boolean userChangeable) {
        this.code = code;
        this.description = description;
        this.defaultEnabled = defaultEnabled;
        this.userChangeable = userChangeable;
    }
    
    /**
     * 获取配置代码
     * @return 配置代码 (1-4)
     */
    public int getCode() {
        return code;
    }
    
    /**
     * 获取配置描述
     * @return 配置描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取默认启用状态
     * @return true表示默认启用，false表示默认禁用
     */
    public boolean isDefaultEnabled() {
        return defaultEnabled;
    }
    
    /**
     * 是否允许用户更改
     * @return true表示用户可以更改，false表示用户无法更改
     */
    public boolean isUserChangeable() {
        return userChangeable;
    }
    
    /**
     * 根据代码获取配置状态
     * @param code 配置代码
     * @return 对应的配置状态，如果代码无效则返回DEFAULT_OFF_USER_CHANGEABLE
     */
    public static ConfigState fromCode(int code) {
        for (ConfigState state : values()) {
            if (state.code == code) {
                return state;
            }
        }
        return DEFAULT_OFF_USER_CHANGEABLE; // 默认返回可更改的关闭状态
    }
    
    /**
     * 根据字符串代码获取配置状态
     * @param codeStr 配置代码字符串
     * @return 对应的配置状态，如果代码无效则返回DEFAULT_OFF_USER_CHANGEABLE
     */
    public static ConfigState fromCodeString(String codeStr) {
        try {
            int code = Integer.parseInt(codeStr);
            return fromCode(code);
        } catch (NumberFormatException e) {
            return DEFAULT_OFF_USER_CHANGEABLE;
        }
    }
    
    @Override
    public String toString() {
        return "ConfigState{" +
                "code=" + code +
                ", description='" + description + '\'' +
                ", defaultEnabled=" + defaultEnabled +
                ", userChangeable=" + userChangeable +
                '}';
    }
}
