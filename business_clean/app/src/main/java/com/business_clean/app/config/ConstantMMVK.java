package com.business_clean.app.config;

/**
 * 存储的Key
 */
public class ConstantMMVK {
    public static String FIRST_ENTRY = "FIRST_ENTRY";///第一次进入APP
    public static String FIRST_CHECK = "FIRST_CHECK";///只要同意过这个check 以后就再也不用了
    public static String TIME = "TIME";///网络时间
    public static String TIME_INIT_BOOT = "TIME_INIT_BOOT";///获取网络时间的时，获取的系统启动时间

    public static String TOKEN = "TOKEN";

    public static String USER_PHONE = "USER_PHONE";
    public static String COMPANY_UUID = "COMPANY_UUID";
    public static String COMPANY_NAME = "COMPANY_NAME";
    public static String ALI_AUTH_UUID = "ALI_AUTH_UUID";
    public static String USER_INFO = "USER_INFO";

    public static String COMPANY_ALL_CLOCK_ADDRESS = "COMPANY_ALL_CLOCK_ADDRESS";
    public static String USER_CLOCK_RADIUS = "USER_CLOCK_RADIUS";
    public static String INIT_DATA = "INIT_DATA";
    public static String IN_CLASS_TIME_LIST = "IN_CLASS_TIME_LIST";
    public static String OUT_CLASS_TIME_LIST = "OUT_CLASS_TIME_LIST";

    //民族列表存到本地
    public static String NATION_LIST = "NATION_LIST";

    //切换环境
    public static String ENVIRONMENT_BASE_URL = "ENVIRONMENT_BASE_URL";
    public static String ENVIRONMENT_BASE_URL_M = "ENVIRONMENT_BASE_URL_M";
    public static String ENVIRONMENT_BASE_URL_M2 = "ENVIRONMENT_BASE_URL_M2";

    //网页的 base
    public static String ENVIRONMENT_BASE_WEB_URL = "ENVIRONMENT_BASE_WEB_URL";
    //切换环境

    //城市
    public static String PROVICE_CITY_AREA_DATA = "PROVICE_CITY_AREA_DATA";

    //全部项目
    public static String ALL_PROJECT_LIST = "ALL_PROJECT_LIST";

    //花名册 左边侧边栏展开的状态
    public static String ROSTER_MENU_STATUS = "ROSTER_MENU_STATUS";
    //记录状态 办理入职时是否需要入职的员工签字确认 1是 2否
    public static String IS_ENTRY_SIGN = "IS_ENTRY_SIGN";
    //是否拍照上传无犯罪证明 1是 2否
    public static String IS_UPLOAD_NOT_GUILTY = "IS_UPLOAD_NOT_GUILTY";
    //是否允许补入今日之前的入职信息 1是 2否
    public static String IS_ENTRY_BEFORE_TODAY = "IS_ENTRY_BEFORE_TODAY";
    //clock_in_range打卡未知范围(米)
    public static String CLOCK_IN_RANGE = "CLOCK_IN_RANGE";
    //is_see_kq是否允许员工查看考勤 1是2否
    public static String IS_SEE_KQ = "IS_SEE_KQ";
    //是否允许员工查看个人相册	is_see_photo
    public static String IS_SEE_PHOTO = "IS_SEE_PHOTO";
    //is_minimalism是否开启极简模式 1是2否
    public static String IS_MINIMALISM = "IS_MINIMALISM";
    //记录创建新人的时候，获取的岗位薪资
    public static String WORK_JOB_SALARY = "WORK_JOB_SALARY";

    public static String JUMP_TODO_PAGE = "JUMP_TODO_PAGE";

    //关闭一键登陆
    public static String AUTO_LOGIN = "AUTO_LOGIN";

    //首页列表的展示方式
    public static String HOME_LIST_TYPE = "HOME_LIST_TYPE";

    //后台时间
    public static String APP_BACKEND_TIME = "APP_BACKEND_TIME";

    //保洁员开启工作拍照
    public static String CLEAN_OPEN_WORK_CAMERA = "CLEAN_OPEN_WORK_CAMERA";
    //保洁员开启打卡
    public static String CLEAN_OPEN_SELF_CLOCK = "CLEAN_OPEN_SELF_CLOCK";

    //存储用户记录
    public static String SAVE_PHOTO_ALBUM = "SAVE_PHOTO_ALBUM";
    //记录更新的版本更新的COde
    public static String APP_UPDATE_VERSION_CODE = "APP_UPDATE_VERSION_CODE";
    ///分享的模式 是分享微信还是企业微信
    public static String SHARE_MODE_TYPE = "SHARE_MODE_TYPE";
    ///高德地图的key
    public static String A_MAP_KEY = "A_MAP_KEY";
    //是否显示 APP 信息
    public static String IS_SHOW_INFO_BITMAP = "IS_SHOW_INFO_BITMAP";

}
