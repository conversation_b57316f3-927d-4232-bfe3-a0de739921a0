package com.business_clean.data.initconfig;

import android.graphics.Bitmap;

import java.io.Serializable;

public class WaterMarkBitmapData implements Serializable {
    //右上
    public static final int POS_LEFT_TOP = 4;
    //居中
    public static final int POS_CENTER = 3;
    //左上
    public static final int POS_RIGHT_TOP = 1;
    //左下 默认是左下
    public static final int POS_RIGHT_BOTTOM = 2;
    private Bitmap originalBitmap;//原图
    private Bitmap waterBitmap;//水印图
    private Bitmap keyBitmap;//防伪码的图
    private Bitmap customLogoBitmap;//自定义的图
    private int customPos = POS_RIGHT_BOTTOM;

    private double lat;
    private double lnt;

    private String Address;

    public double getLat() {
        return lat;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public double getLnt() {
        return lnt;
    }

    public void setLnt(double lnt) {
        this.lnt = lnt;
    }

    public String getAddress() {
        return Address;
    }

    public void setAddress(String address) {
        Address = address;
    }

    public Bitmap getOriginalBitmap() {
        return originalBitmap;
    }

    public void setOriginalBitmap(Bitmap originalBitmap) {
        this.originalBitmap = originalBitmap;
    }

    public Bitmap getWaterBitmap() {
        return waterBitmap;
    }

    public void setWaterBitmap(Bitmap waterBitmap) {
        this.waterBitmap = waterBitmap;
    }

    public Bitmap getKeyBitmap() {
        return keyBitmap;
    }

    public void setKeyBitmap(Bitmap keyBitmap) {
        this.keyBitmap = keyBitmap;
    }

    public void setCustomLogoBitmap(Bitmap customLogoBitmap) {
        this.customLogoBitmap = customLogoBitmap;
    }

    public void setCustomPos(int customPos) {
        this.customPos = customPos;
    }

    public Bitmap getCustomLogoBitmap() {
        return customLogoBitmap;
    }

    public int getCustomPos() {
        return customPos;
    }
}
