package com.business_clean.data.mode.camera;

import java.io.Serializable;

public class CameraConfigData implements Serializable {

    private String uuid;
    private String configuration_name;
    private String introduction;
    private String save_photo_mobile;
    private String open_work_photo;
    private String dk_confirm;
    private String dk_switch_camera;
    private String watermark_logo;
    private String watermark_logo_pos;
    private String watermark_logo_width;
    private String watermark_logo_height;
    private String watermark_logo_scale;
    private String is_show_app;
    private String is_see_kq;
    private String is_see_photo;
    private String is_minimalism;


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getConfiguration_name() {
        return configuration_name;
    }

    public void setConfiguration_name(String configuration_name) {
        this.configuration_name = configuration_name;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getSave_photo_mobile() {
        return save_photo_mobile;
    }

    public void setSave_photo_mobile(String save_photo_mobile) {
        this.save_photo_mobile = save_photo_mobile;
    }

    public String getOpen_work_photo() {
        return open_work_photo;
    }

    public void setOpen_work_photo(String open_work_photo) {
        this.open_work_photo = open_work_photo;
    }

    public String getDk_confirm() {
        return dk_confirm;
    }

    public void setDk_confirm(String dk_confirm) {
        this.dk_confirm = dk_confirm;
    }

    public String getDk_switch_camera() {
        return dk_switch_camera;
    }

    public void setDk_switch_camera(String dk_switch_camera) {
        this.dk_switch_camera = dk_switch_camera;
    }

    public String getWatermark_logo() {
        return watermark_logo;
    }

    public void setWatermark_logo(String watermark_logo) {
        this.watermark_logo = watermark_logo;
    }

    public String getWatermark_logo_pos() {
        return watermark_logo_pos;
    }

    public void setWatermark_logo_pos(String watermark_logo_pos) {
        this.watermark_logo_pos = watermark_logo_pos;
    }

    public String getWatermark_logo_width() {
        return watermark_logo_width;
    }

    public void setWatermark_logo_width(String watermark_logo_width) {
        this.watermark_logo_width = watermark_logo_width;
    }

    public String getWatermark_logo_height() {
        return watermark_logo_height;
    }

    public void setWatermark_logo_height(String watermark_logo_height) {
        this.watermark_logo_height = watermark_logo_height;
    }

    public void setWatermark_logo_scale(String watermark_logo_scale) {
        this.watermark_logo_scale = watermark_logo_scale;
    }

    public String getWatermark_logo_scale() {
        return watermark_logo_scale;
    }

    public void setIs_show_app(String is_show_app) {
        this.is_show_app = is_show_app;
    }

    public String getIs_show_app() {
        return is_show_app;
    }

    public String getIs_see_kq() {
        return is_see_kq;
    }

    public void setIs_see_kq(String is_see_kq) {
        this.is_see_kq = is_see_kq;
    }

    public String getIs_see_photo() {
        return is_see_photo;
    }

    public void setIs_see_photo(String is_see_photo) {
        this.is_see_photo = is_see_photo;
    }

    public String getIs_minimalism() {
        return is_minimalism;
    }

    public void setIs_minimalism(String is_minimalism) {
        this.is_minimalism = is_minimalism;
    }


    @Override
    public String toString() {
        return "CameraConfigData{" +
                "uuid='" + uuid + '\'' +
                ", configuration_name='" + configuration_name + '\'' +
                ", introduction='" + introduction + '\'' +
                ", save_photo_mobile='" + save_photo_mobile + '\'' +
                ", open_work_photo='" + open_work_photo + '\'' +
                ", dk_confirm='" + dk_confirm + '\'' +
                ", dk_switch_camera='" + dk_switch_camera + '\'' +
                ", watermark_logo='" + watermark_logo + '\'' +
                ", watermark_logo_pos='" + watermark_logo_pos + '\'' +
                ", watermark_logo_width='" + watermark_logo_width + '\'' +
                ", watermark_logo_height='" + watermark_logo_height + '\'' +
                ", watermark_logo_scale='" + watermark_logo_scale + '\'' +
                ", is_show_app='" + is_show_app + '\'' +
                ", is_see_kq='" + is_see_kq + '\'' +
                ", is_see_photo='" + is_see_photo + '\'' +
                ", is_minimalism='" + is_minimalism + '\'' +
                '}';
    }
}
