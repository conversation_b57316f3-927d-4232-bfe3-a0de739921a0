package com.business_clean.ui.activity;

import androidx.annotation.Nullable;
import androidx.lifecycle.Observer;

import android.os.Bundle;
import android.text.TextUtils;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.config.AsyncRequestUtil;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.flutter.FlutterManager;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.TimeClockUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.weight.dialog.UserPrivacyPolicyPopup;
import com.business_clean.data.initconfig.MapInfo;
import com.business_clean.data.initconfig.TimestampEntity;
import com.business_clean.data.mode.camera.CameraConfigData;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.databinding.ActivityStartBinding;
import com.business_clean.ui.activity.login.LoginActivity;
import com.business_clean.ui.activity.main.MainActivity;
import com.business_clean.viewmodel.request.StartVideModel;
import com.google.gson.Gson;
import com.lxj.xpopup.XPopup;

import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;
import me.hgj.mvvmhelper.util.LogXmManager;

/**
 * 启动界面
 */
public class StartActivity extends BaseActivity<StartVideModel, ActivityStartBinding> {

    private String TAG = "StartActivity";

    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        NetUrl.defaultUrl = MMKVHelper.getString(ConstantMMVK.ENVIRONMENT_BASE_URL, NetUrl.SERVER_URL_RELEASE);

        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            FlutterManager.sServerType = FlutterManager.SERVER_TYPE_RELEASE;
        } else if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_TEST)) {
            FlutterManager.sServerType = FlutterManager.SERVER_TYPE_TEST;
        } else {
            FlutterManager.sServerType = FlutterManager.SERVER_TYPE_DEV;
        }

        ScreenUtils.setFullScreen(StartActivity.this);
        LogExtKt.logE("获取应用签名的的 SHA1 值 --> " + AppUtils.getAppSignaturesSHA1(), TAG);
        LogExtKt.logE("获取应用签名的的 SHA256 值 --> " + AppUtils.getAppSignaturesSHA256(), TAG);
        LogExtKt.logE("获取应用签名的的 MD5 值 --> " + AppUtils.getAppSignaturesMD5(), TAG);

//        MMKVHelper.putString(ConstantMMVK.TOKEN, "0fc01fa360d1267cd1fc28ddefa4242e");


        //是否第一次打开App（隐私政策）
        boolean firstEntryApp = MMKVHelper.getBoolean(ConstantMMVK.FIRST_ENTRY, false);
        if (!firstEntryApp) {//弹窗、隐私政策
            showDialogUserPrivacyPolicy();
        } else {
            mViewModel.requestTime();
            AsyncRequestUtil.requestRefreshToken();
        }
    }

    /**
     * 显示弹窗
     */
    private void showDialogUserPrivacyPolicy() {
        new XPopup.Builder(this)
                .dismissOnBackPressed(false)
                .dismissOnTouchOutside(false)
                .enableDrag(false)//关闭拖拽
                .asCustom(new UserPrivacyPolicyPopup(StartActivity.this, new OnDialogConfirmListener() {
                    @Override
                    public void onConfirm() {
                        MMKVHelper.putBoolean(ConstantMMVK.FIRST_ENTRY, true);
                        CommonUtils.onDelayMainProcessInit();//延迟初始化
                        mViewModel.requestTime();
                    }
                })).show();
    }


    @Override
    public void onRequestSuccess() {
        super.onRequestSuccess();
        //监听错误
        mViewModel.getErrorMapEntity().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                ToastUtil.show("地图服务初始化失败，请检查网络");
            }
        });
        ///获取高德的内容后再初始化
        mViewModel.getMapInfo().observe(this, new Observer<MapInfo>() {
            @Override
            public void onChanged(MapInfo mapInfo) {
                MMKVHelper.putString(ConstantMMVK.A_MAP_KEY, mapInfo.getMap_value());
                CommonUtils.onDelayMapInit(StartActivity.this);
                requestCompanyConfig();
            }
        });

        mViewModel.getTimestampEntity().observe(this, new Observer<TimestampEntity>() {
            @Override
            public void onChanged(TimestampEntity timestampEntity) {
                LogXmManager.log("进入Start界面 写入时间");
                if (timestampEntity != null && !TextUtils.isEmpty(timestampEntity.getTimestamp())) {
                    MMKVHelper.putString(ConstantMMVK.TIME, "" + (Long.parseLong(timestampEntity.getTimestamp()) * 1000));
                    MMKVHelper.putString(ConstantMMVK.TIME_INIT_BOOT, "" + TimeClockUtil.getCurrentSystemUptimeInMillis());
                }
                mViewModel.requestAMapInfo();
            }
        });

        mViewModel.getErrorEntity().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                LogXmManager.log("进入Start界面 没有获取到网络时间，清空");
                MMKVHelper.remove(ConstantMMVK.TIME);
                MMKVHelper.remove(ConstantMMVK.TIME_INIT_BOOT);
                //先进去界面 走以前的配置
                gotoOtherActivity();
            }
        });


        mViewModel.getCameraConfigData().observe(this, new Observer<CameraConfigData>() {
            @Override
            public void onChanged(CameraConfigData configData) {
                //存储方案，如果报错就从本地取
                MMKVHelper.putString(ConstantMMVK.USER_CONFIG, JSON.toJSONString(configData));
                gotoOtherActivity(configData);
            }
        });

        //请求接口失败，直接跳转
        mViewModel.getWorkRulesErrorEntity().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                //先进去界面 走以前的配置
                String json = MMKVHelper.getString(ConstantMMVK.USER_CONFIG);
                if (!TextUtils.isEmpty(json)) {
                    CameraConfigData userInfo = new Gson().fromJson(json, CameraConfigData.class);
                    if (userInfo != null) {
                        gotoOtherActivity(userInfo);
                    } else {
                        gotoOtherActivity();
                    }
                } else {
                    gotoOtherActivity();
                }
            }
        });
    }

    private void requestCompanyConfig() {
        if (!TextUtils.isEmpty(MMKVHelper.getString(ConstantMMVK.TOKEN))) {
            mViewModel.requestUserWorkRules();
        } else {
            gotoOtherActivity();
        }
    }


    /**
     * 请求企业配置后走的
     *
     * @param configData
     */
    private void gotoOtherActivity(CameraConfigData configData) {
        //初始化企业配置
        if (!TextUtils.isEmpty(MMKVHelper.getString(ConstantMMVK.TOKEN))) {
            //获取本地
            String userInfoJson = MMKVHelper.getString(ConstantMMVK.USER_INFO);
            if (!TextUtils.isEmpty(userInfoJson)) {
                UserInfo userInfo = new Gson().fromJson(userInfoJson, UserInfo.class);
                if (userInfo == null) {
                    ActivityForwardUtil.startActivity(LoginActivity.class);
                    AppExtKt.finishActivity(StartActivity.class);
                    return;
                }
                CommonUtils.updateLocalUserData(userInfo);
                CommonUtils.updateUserOneConfig(configData);
                if (userInfo.getUser() != null && Constant.ROLE_CLEAN_ID.equals(userInfo.getUser().getRole_id())) {
                    //加一层判断，是否开启极简模式 默认是false
                    if (!MMKVHelper.getBoolean(ConstantMMVK.IS_MINIMALISM, true)) {
                        Bundle bundle = new Bundle();
                        bundle.putBoolean("openCamera", true);
                        ActivityForwardUtil.startActivity(MainActivity.class, bundle);
                    } else {
                        ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                    }
                } else {
                    ActivityForwardUtil.startActivity(MainActivity.class);
                }
            } else {
                ActivityForwardUtil.startActivity(MainActivity.class);
            }
        } else {
            ActivityForwardUtil.startActivity(LoginActivity.class);
        }
        AppExtKt.finishActivity(StartActivity.class);
    }

    /**
     * 跳转到其他activity的逻辑
     */
    private void gotoOtherActivity() {
        if (!TextUtils.isEmpty(MMKVHelper.getString(ConstantMMVK.TOKEN))) {
            //获取本地
            String userInfoJson = MMKVHelper.getString(ConstantMMVK.USER_INFO);
            if (!TextUtils.isEmpty(userInfoJson)) {
                UserInfo userInfo = new Gson().fromJson(userInfoJson, UserInfo.class);
                if (userInfo == null) {
                    ActivityForwardUtil.startActivity(LoginActivity.class);
                    AppExtKt.finishActivity(StartActivity.class);
                    return;
                }
                CommonUtils.updateLocalUserData(userInfo);
                if (userInfo.getUser() != null && Constant.ROLE_CLEAN_ID.equals(userInfo.getUser().getRole_id())) {
                    //加一层判断，是否开启极简模式 默认是false
                    if (!MMKVHelper.getBoolean(ConstantMMVK.IS_MINIMALISM, true)) {
                        Bundle bundle = new Bundle();
                        bundle.putBoolean("openCamera", true);
                        ActivityForwardUtil.startActivity(MainActivity.class, bundle);
                    } else {
                        ActivityForwardUtil.startActivity(CheckPermissionActivity.class);
                    }
                } else {
                    ActivityForwardUtil.startActivity(MainActivity.class);
                }
            } else {
                ActivityForwardUtil.startActivity(MainActivity.class);
            }
        } else {
            ActivityForwardUtil.startActivity(LoginActivity.class);
        }
        AppExtKt.finishActivity(StartActivity.class);
    }


}