package com.business_clean.ui.activity.camera;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.graphics.Bitmap;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.config.Constant;

import com.business_clean.app.ext.CustomViewExtKt;
import com.business_clean.app.service.ServerReporter;
import com.business_clean.app.util.AnimationUtils;
import com.business_clean.app.util.ImageCompressor;
import com.business_clean.app.util.Mp4Utils;
import com.business_clean.app.util.PhotoBitmapUtils;
import com.business_clean.app.util.RxHelper;
import com.business_clean.app.util.ShareUtils;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.share.ShareHelperTools;
import com.business_clean.app.util.share.ShareParams;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.data.initconfig.WaterMarkBitmapData;
import com.business_clean.data.mode.camera.TaskSection;
import com.business_clean.data.mode.camera.TeamClassEntity;
import com.business_clean.data.mode.camera.TeamClassListEntity;
import com.business_clean.data.mode.camera.TodayTaskEntity;
import com.business_clean.data.mode.camera.TodayTaskEntityList;
import com.business_clean.data.mode.camera.TodoTitleEntity;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.databinding.ActivityEditPhotoBinding;
import com.business_clean.ui.adapter.camera.TaskAdapter;
import com.business_clean.ui.adapter.camera.TeamClockAdapter;
import com.business_clean.viewmodel.request.CameraViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemChildClickListener;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.gyf.immersionbar.ImmersionBar;
import com.king.drawboard.draw.Draw;
import com.king.drawboard.view.DrawBoardView;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.lxj.xpopup.XPopup;

import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.functions.Consumer;
import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;

public class EditPhotoActivity extends BaseActivity<CameraViewModel, ActivityEditPhotoBinding> {

    private String TAG = "EditPhotoActivity";
    private Bitmap originalBitmap;//原始图片
    private Bitmap waterBitmap;//水印图片
    private Bitmap keyBitmap;//防伪码的图片
    private Bitmap customLogoBitmap;//自定义的图片
    private int posPosition;//自定义图片的位置
    private int channel;//用来区分是图片/视频
    private String videoPath;

    public List<String> mp4List = new ArrayList<>();
    private String takePhotoMills;//时间戳

    private int act_type = 2;//来区分是打卡、工作拍照、集体打卡

    private WaterMarkBitmapData waterMarkBitmapData;

    private TaskAdapter taskAdapter;

    private String act_type_string;

    private String project_uuid;

    //集体打卡的Adapter
    private TeamClockAdapter mAdapter;

    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    protected void initImmersionBar() {
        super.initImmersionBar();
        ImmersionBar.with(this)
                .statusBarColor(R.color.black)
                .navigationBarColor(R.color.white)
                .titleBar(mDatabind.rlTab)
                .init();
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        initIntent();
        fillProject();
        //待办任务
        taskAdapter = new TaskAdapter();
        mDatabind.recyclerTask.setAdapter(taskAdapter);
        mDatabind.recyclerTask.setLayoutManager(new LinearLayoutManager(this));
        taskAdapter.setEmptyView(R.layout.empty_view);

        //集体打卡
        mAdapter = new TeamClockAdapter();
        mDatabind.recycler.setAdapter(mAdapter);
        mDatabind.recycler.setLayoutManager(new LinearLayoutManager(this));
        mAdapter.setEmptyView(CustomViewExtKt.setAdapterEmpty("暂无人员", false));
        //设置默认出勤人数
        setAttendanceText(0);
    }

    private void initIntent() {
        if (getIntent() != null && getIntent().getExtras() != null) {
            channel = getIntent().getExtras().getInt("channel");
            takePhotoMills = getIntent().getExtras().getString("takePhotoMills");
            act_type_string = getIntent().getExtras().getString("act_type");
            project_uuid = getIntent().getExtras().getString("project_uuid");
            //1打卡 2领班打卡 3日常
            switch (act_type_string) {
                case "工作拍照":
                    act_type = 3;
                    mDatabind.llTeamLayout.setVisibility(View.INVISIBLE);
                    mDatabind.tvTitle.setText("工作拍照");
                    break;
                case "集体打卡":
                    act_type = 2;
                    mDatabind.tvTitle.setText("集体打卡");
                    mViewModel.requestTeamClassList(project_uuid);
                    break;
                case "打卡":
                    act_type = 1;
                    mDatabind.tvTitle.setText("打卡");
                    mDatabind.llTeamLayout.setVisibility(View.INVISIBLE);
                    break;
                default:
                    act_type = -1;
                    mDatabind.tvTitle.setText(act_type_string);
                    mDatabind.recycler.setVisibility(View.GONE);
                    mDatabind.llTaskLayout.setVisibility(View.VISIBLE);
                    break;
            }

            LogUtils.e("我来编辑界面了 时间戳是： " + takePhotoMills);
            if (App.getAppViewModelInstance().getWaterMarkBitmap().getValue() != null) {
                waterMarkBitmapData = App.getAppViewModelInstance().getWaterMarkBitmap().getValue();
            }
            if (1 == channel) {//图片
                initPaintMode();
                if (waterMarkBitmapData != null) {
                    originalBitmap = waterMarkBitmapData.getOriginalBitmap();
                    mDatabind.drawView.setImageBitmap(waterMarkBitmapData.getOriginalBitmap());
                    waterBitmap = waterMarkBitmapData.getWaterBitmap();
                    keyBitmap = waterMarkBitmapData.getKeyBitmap();
                    customLogoBitmap = waterMarkBitmapData.getCustomLogoBitmap();
                    posPosition = waterMarkBitmapData.getCustomPos();
                    mDatabind.drawView.setWatermarkImages(
                            ImageUtils.scale(waterMarkBitmapData.getWaterBitmap(), 0.6f, 0.6f),
                            ImageUtils.scale(waterMarkBitmapData.getKeyBitmap(), 0.8f, 0.8f),
                            waterMarkBitmapData.getCustomLogoBitmap(),
                            waterMarkBitmapData.getCustomPos());
                }
                mDatabind.flPhotoLayout.setVisibility(View.VISIBLE);
                mDatabind.flVideoLayout.setVisibility(View.GONE);

            } else if (2 == channel) {//视频
                mDatabind.flPhotoLayout.setVisibility(View.GONE);
                mDatabind.flVideoLayout.setVisibility(View.VISIBLE);
                videoPath = getIntent().getExtras().getString("video_path");
                mDatabind.video.setVideoPath(videoPath);
                mDatabind.video.start();
                initVideoListener();
            } else {
                ToastUtil.show("未拿到任何数据");
            }
        }


        if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR) {
            mDatabind.ivChange.setVisibility(View.VISIBLE);
        } else {
            mDatabind.ivChange.setVisibility(View.GONE);
        }

    }

    private void initPaintMode() {
        mDatabind.drawView.setZoomEnabled(false);//是否启用缩放
        mDatabind.drawView.setLineStrokeWidth(8f);
        //绘图监听，绘制了后，再显示清除按钮
        mDatabind.drawView.setOnDrawListener(new DrawBoardView.OnDrawListener() {
            @Override
            public void onDraw(Draw draw) {
                if (mDatabind.llErase.getVisibility() == View.GONE) {
                    AnimationUtils.slideInFromLeft(mDatabind.llErase);
                }
            }
        });
    }

    /**
     * 监听播放器的方法
     */
    private void initVideoListener() {
        mDatabind.video.setOnCompletionListener(new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                mDatabind.ivVideoStatus.setImageResource(R.mipmap.icon_camera_play);
            }
        });
    }

    @Override
    public void initObserver() {
        //切换项目
        App.getAppViewModelInstance().getProjectInfo().observe(this, new Observer<ProjectMangerList>() {
            @Override
            public void onChanged(ProjectMangerList projectMangerList) {
                fillProject();
            }
        });

        //查询集体打卡 有多少人
        App.getAppViewModelInstance().getCheckTeamNumber().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                int number = 0;
                //这里循环查询 到底有多少个 已经选中的
                for (int i = 0; i < mAdapter.getData().size(); i++) {
                    for (int j = 0; j < mAdapter.getData().get(i).getClassUserEntity().getList().size(); j++) {
                        if (mAdapter.getData().get(i).getClassUserEntity().getList().get(j).isSelected()) {
                            number += 1;
                        }
                    }
                }
                //最后设置
                setAttendanceText(number);
            }
        });
    }

    @Override
    public void onBindViewClick() {
        //集体打卡
        mAdapter.setOnItemChildClickListener(new OnItemChildClickListener() {
            @Override
            public void onItemChildClick(@NonNull BaseQuickAdapter adapter, @NonNull View view, int position) {
                if (view.getId() == R.id.tv_item_team_clock_more) {//如果超过20个就加载更多
                    mViewModel.requestTeamClassUserList(mAdapter.getData().get(position).getUuid(),
                            mAdapter.getData().get(position).getClassUserEntity().getPage() + 1, mAdapter.getData().get(position));
                }
            }
        });
        //任务待办
        taskAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                if (taskAdapter.getData().get(position).getObject() instanceof TodayTaskEntityList) {
                    TodayTaskEntityList taskEntityList = (TodayTaskEntityList) taskAdapter.getData().get(position).getObject();
//                    taskEntityList.setChoice(!taskEntityList.isChoice());
                    taskAdapter.updateTask(taskEntityList.getUuid());

                }
//                taskAdapter.notifyDataSetChanged();
            }
        });

        //清除图片上一步操作
        mDatabind.llErase.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDatabind.drawView.clear();
                AnimationUtils.slideOutToRight(mDatabind.llErase);
            }
        });


        //视频的暂停播放
        mDatabind.ivVideoStatus.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mDatabind.video.isPlaying()) {
                    mDatabind.ivVideoStatus.setImageResource(R.mipmap.icon_camera_play);
                    mDatabind.video.pause();
                } else {
                    mDatabind.video.start();
                    mDatabind.ivVideoStatus.setImageResource(0);
                }
            }
        });
        //保存
        mDatabind.butBottomButNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (act_type == -1 && TextUtils.isEmpty(taskAdapter.getUuid())) {
                    ToastUtil.show("请选择任务");
                    return;
                }
                //判断是否可以保存图片
                saveFile();
                AppExtKt.finishActivity(EditPhotoActivity.class);
            }
        });
        //分享到企业微信，并保存
        mDatabind.butBottomButOther.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (1 == channel) {//分享图片
                    Bitmap waterMarkBitmap = PhotoBitmapUtils.drawWaterToBitMap2(EditPhotoActivity.this,
                            waterBitmap, mDatabind.drawView.getResultBitmap(true), keyBitmap, customLogoBitmap, posPosition);
                    File shareImage = ImageUtils.save2Album(waterMarkBitmap, Bitmap.CompressFormat.JPEG);
                    ShareUtils.shareBitmapSys(EditPhotoActivity.this, shareImage);
                } else {
                    shareLocalVideo();//分享本地视频
                }
                saveFile();
                finish();
            }
        });
        //分享到企业微信，并保存
        mDatabind.butBottomButLast.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (1 == channel) {//分享图片
                    Bitmap waterMarkBitmap = PhotoBitmapUtils.drawWaterToBitMap2(EditPhotoActivity.this,
                            waterBitmap, mDatabind.drawView.getResultBitmap(true), keyBitmap, customLogoBitmap, posPosition);
                    if (waterMarkBitmap != null) {
                        ShareParams params = new ShareParams();
                        params.setBitmap(waterMarkBitmap);
                        ShareHelperTools.getInstance().shareImage(params, EditPhotoActivity.this);
                    } else {
                        ToastUtil.show("无效的图片");
                    }
                } else {
                    shareLocalVideo();//分享本地视频
                }
                saveFile();
                finish();
            }
        });

        //关闭界面
        mDatabind.backButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        //切换 项目
        mDatabind.llChangeProject.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_SUPER_MANGER || Constant.ROLE_MANGER || Constant.ROLE_HR) {
                    new XPopup.Builder(EditPhotoActivity.this)
                            .autoFocusEditText(false)
                            .asCustom(new PagerDrawerPopup(EditPhotoActivity.this)).show();
                }
            }
        });
    }


    /**
     * 这里是实际出勤人数
     */
    public void setAttendanceText(int actualAttendance) {
        // 创建文本内容
        String text = "实际出勤 " + actualAttendance + " 人";

        // 创建 SpannableString
        SpannableString spannableString = new SpannableString(text);

        // 设置数字部分为绿色
        int start = text.indexOf(String.valueOf(actualAttendance));
        int end = start + String.valueOf(actualAttendance).length();
        spannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(this, R.color.base_primary)), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        // 将 SpannableString 设置给 TextView
        mDatabind.tvTeamNum.setText(spannableString);
    }


    /**
     * 分享视频出去
     */
    private void shareLocalVideo() {
        if (TextUtils.isEmpty(videoPath)) {
            return;
        }
        ShareUtils.shareWechatFriend(this, new File(videoPath));
    }

    //合成视频
    private void mergeMp4List() {
        String savePath = Constant.CAMERA_SAVE_PHOTO_VIDEO ? Constant.EXTERNAL_PHOTO_PATH : Constant.INTERNAL_VIDEO_PATH;
        Mp4Utils.mergeMp4List(mp4List, new File(savePath + "/" + System.currentTimeMillis() + "mergeVideo.mp4")).compose(RxHelper.rxSchedulerHelper()).subscribe(new Consumer<File>() {
            @Override
            public void accept(File mergeVideo) throws Exception {
                if (Constant.CAMERA_SAVE_PHOTO_VIDEO) {
                    Mp4Utils.saveVideoToAlbum(EditPhotoActivity.this, mergeVideo.getAbsolutePath());
                    FileUtils.notifySystemToScan(mergeVideo);
                }
                mp4List.clear();
                LogExtKt.logE("合并了完成了", TAG);
            }
        }, throwable -> {
            throwable.printStackTrace();
            LogExtKt.logE("合并出现异常" + throwable.getMessage(), TAG);
        });
    }


    private void fillProject() {
        if (App.getAppViewModelInstance().getProjectInfo().getValue() != null) {
            ProjectMangerList mangerList = App.getAppViewModelInstance().getProjectInfo().getValue();
            if (!TextUtils.isEmpty(mangerList.getProject_short_name())) {
                mDatabind.tvSubTitle.setVisibility(View.VISIBLE);
                mDatabind.tvSubTitle.setText(mangerList.getProject_short_name());
            } else {
                mDatabind.tvSubTitle.setVisibility(View.GONE);
            }
        }

        //请求任务类型
        if (act_type == -1) {
            switch (act_type_string) {
                case "清洁":
                    mViewModel.getToDayTask("1", project_uuid);
                    break;
                case "巡检":
                    mViewModel.getToDayTask("2", project_uuid);
                    break;
                case "培训":
                    mViewModel.getToDayTask("3", project_uuid);
                    break;
                case "办结工单":
                    mViewModel.getToDayTask("20", project_uuid);
                    break;
            }
        }
    }


    /**
     * 因为保存跟分享都会走这个方法，所以封装起来了
     *
     * @return
     */
    private void saveFile() {
        if (1 == channel) {
            //转换水印照片
            Bitmap waterMarkBitmap = PhotoBitmapUtils.drawWaterToBitMap2(this, waterBitmap, mDatabind.drawView.getResultBitmap(true), keyBitmap, customLogoBitmap, posPosition);

            //先存到本地项目
            File file = saveBitmapToFile(waterMarkBitmap, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + ".jpg");//水印拿到一个暂存的路径
            File thumbFile = saveBitmapToFile(PhotoBitmapUtils.getBitmapThumb(file.getAbsolutePath(), 200), Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "thumb" + ".jpg");
            File originaFile = saveBitmapToFile(originalBitmap, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "origina" + ".jpg");//原图拿到一个暂存的路径

            //先压缩水印图片
            ImageCompressor.compressBitmap(this, file.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                @Override
                public void onCallback(String srcPath, String resultWaterPath) {
                    //原图的压缩 压缩完成后 再去上传服务器，因为服务器要原图跟水印图，所以都需要压缩后 处理
                    ImageCompressor.compressBitmap(EditPhotoActivity.this,
                            originaFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                                @Override
                                public void onCallback(String srcPath, String resultPath) {
                                    //原图的压缩 压缩完成后 再去上传服务器，因为服务器要原图跟水印图，所以都需要压缩后 处理
                                    ImageCompressor.compressBitmap(EditPhotoActivity.this,
                                            thumbFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                                                @Override
                                                public void onCallback(String srcPath, String thumbPath) {
                                                    LogUtils.e("压缩完成后的图片 resultPath -->" + resultPath);
                                                    //拿到压缩后的路径了，那么就进行存储
                                                    if (Constant.CAMERA_SAVE_PHOTO_VIDEO) {//开启本地照片存储 需要保存水印照片
                                                        LogUtils.e("压缩完成后的图片 保存本地了");
                                                        ImageUtils.save2Album(waterMarkBitmap, Constant.EXTERNAL_PHOTO_PATH, Bitmap.CompressFormat.JPEG);
//                                                        //是否需要保存原始图片到相册
//                                                        if (Constant.CAMERA_SAVE_ORIGINAL_PHOTO) {
//                                                            ImageUtils.save2Album(BitmapFactory.decodeFile(resultPath), Bitmap.CompressFormat.JPEG);
//                                                        }
                                                    }
                                                    uploadMedia(1, resultWaterPath, resultPath, thumbPath);
                                                }
                                            });
                                }
                            });
                }
            });
        } else if (2 == channel) {//视频类型
            Bitmap thumb = PhotoBitmapUtils.getVideoThumbnail(videoPath, 90, 160, MediaStore.Video.Thumbnails.MINI_KIND);

            File thumbFile = saveBitmapToFile(thumb, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "video_thumb" + ".jpg");
            ImageCompressor.compressBitmap(EditPhotoActivity.this, thumbFile.getAbsolutePath(), 10,
                    new OnKeyValueResultCallbackListener() {
                        @Override
                        public void onCallback(String srcPath, String thumbPath) {
                            LogUtils.e("压缩完成后的图片 resultPath -->" + thumbPath);
                            //保存视频
                            mp4List.add(videoPath);
                            mergeMp4List();
                            uploadMedia(2, videoPath, null, thumbPath);
                        }
                    });
        }
    }


    //图片转换成File文件
    private File saveBitmapToFile(Bitmap waterMarkBitmap, String savePath, String finalFileName) {
        File file = new File(savePath + finalFileName);
        ImageUtils.save(waterMarkBitmap, savePath + finalFileName, Bitmap.CompressFormat.JPEG);
        return file;
    }

    /**
     * 上传图片的操作
     * message_type 区分是图片还是视频
     * file 水印的
     * original 无水印的
     */
    private void uploadMedia(int message_type, String file, String original, String thumb) {
        if (waterMarkBitmapData == null) {
            return;
        }
        if (TextUtils.isEmpty(takePhotoMills)) {
            takePhotoMills = String.valueOf(0);
        }

        //说明不是任务工作计划
        if (act_type != -1) {

        } else {
            if (TextUtils.isEmpty(taskAdapter.getUuid())) {
                ToastUtil.show("请选择任务");
                return;
            }
        }


        //数据给数据库
        ServerReporter.getInstance()
                .reportToServerDb(project_uuid, taskAdapter.getUuid(), act_type, message_type, waterMarkBitmapData.getLat(),
                        waterMarkBitmapData.getLnt(), waterMarkBitmapData.getAddress(), file, original, thumb,
                        mAdapter.getTeamUserList(), Long.parseLong(takePhotoMills));
        //清除水印
        App.getAppViewModelInstance().getWaterMarkBitmap().setValue(null);
    }


    @Override
    public void onRequestSuccess() {
        //获取所有的班次列表
        mViewModel.getClassTeamEntity().observe(this, new Observer<TeamClassEntity>() {
            @Override
            public void onChanged(TeamClassEntity teamClassEntity) {
                if (teamClassEntity.getList().size() > 0) {
                    for (TeamClassListEntity teamClassList : teamClassEntity.getList()) {
                        //请求这个班次下的，所有人员，请求成功后，组装到teamClassEntity
                        mViewModel.requestTeamClassUserList(teamClassList.getUuid(), 1, teamClassList);
                    }
                }
            }
        });
        //获取当前的用户列表
        mViewModel.getClassTeamUserEntity().observe(this, new Observer<TeamClassListEntity>() {
            @Override
            public void onChanged(TeamClassListEntity teamClassListEntity) {
                mAdapter.addData(teamClassListEntity);
                mAdapter.notifyDataSetChanged();
            }
        });


        //进入任务办结
        mViewModel.getTodayTaskEntity().observe(this, new Observer<TodayTaskEntity>() {
            @Override
            public void onChanged(TodayTaskEntity todayTaskEntity) {
                //把俩个数组列表组成一个
                List<TaskSection> sectionList = new ArrayList<>();
                if (todayTaskEntity.getTodo_task_list().size() > 0) {
                    sectionList.add(new TaskSection(true, new TodoTitleEntity("待办：", "")));
                }
                for (TodayTaskEntityList task : todayTaskEntity.getTodo_task_list()) {
                    sectionList.add(new TaskSection(false, task));
                }
                if (todayTaskEntity.getFinished_task_list().size() > 0) {
                    sectionList.add(new TaskSection(true, new TodoTitleEntity("今日办结任务：", "已办结的任务还可以追加图片")));
                }
                for (TodayTaskEntityList finishTask : todayTaskEntity.getFinished_task_list()) {
                    sectionList.add(new TaskSection(false, finishTask));
                }
                taskAdapter.setList(sectionList);
            }
        });
    }


    @Override
    protected void onResume() {
        super.onResume();
        mDatabind.video.resume();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mDatabind.video.pause();
    }


    @Override
    protected void onStop() {
        super.onStop();
        if (mDatabind.video != null) {
            mDatabind.video.stopPlayback();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        recycleBitmap(originalBitmap);
        recycleBitmap(waterBitmap);
        recycleBitmap(keyBitmap);
        App.getAppViewModelInstance().getWaterMarkBitmap().setValue(null);
        mDatabind.video.suspend();
    }


    private void recycleBitmap(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
    }
}