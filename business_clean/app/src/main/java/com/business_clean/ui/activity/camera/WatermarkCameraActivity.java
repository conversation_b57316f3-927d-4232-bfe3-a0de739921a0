package com.business_clean.ui.activity.camera;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.LinearSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.content.Context;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.PointF;
import android.location.Location;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;


import com.alibaba.fastjson.JSON;
import com.bumptech.glide.Glide;
import com.business_clean.app.util.GlideUtil;
import com.business_clean.data.mode.camera.CameraConfigData;
import com.google.gson.Gson;
import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationListener;
import com.amap.api.maps.AMapUtils;
import com.amap.api.maps.CoordinateConverter;
import com.amap.api.maps.model.LatLng;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.PhoneUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseActivity;
import com.business_clean.app.callback.OnDialogConfirmListener;
import com.business_clean.app.callback.OnLocationStatusChangeListener;
import com.business_clean.app.config.AsyncRequestUtil;
import com.business_clean.app.config.Constant;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonToFlutter;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.ext.LoadingDialogExtKt;
import com.business_clean.app.network.NetUrl;
import com.business_clean.app.receiver.LocationModeChangeReceiver;
import com.business_clean.app.service.LocService;
import com.business_clean.app.service.ServerReporter;
import com.business_clean.app.util.ActivityForwardUtil;
import com.business_clean.app.util.ImageCompressor;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.Mp4Utils;
import com.business_clean.app.util.PhotoBitmapUtils;
import com.business_clean.app.util.RxHelper;
import com.business_clean.app.util.TimeClockUtil;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.app.util.UploadFileHelper;
import com.business_clean.app.util.appupdate.AppCheckUpdateUtil;
import com.business_clean.app.util.gps.GPSLocationListener;
import com.business_clean.app.util.gps.GPSLocationManager;
import com.business_clean.app.util.gps.GPSProviderStatus;
import com.business_clean.app.weight.camera.CaptureButton;
import com.business_clean.app.weight.camera.CaptureListener;
import com.business_clean.app.weight.dialog.CustomClockInDialog;
import com.business_clean.app.weight.dialog.CustomLocationDialog;
import com.business_clean.app.weight.dialog.PagerDrawerPopup;
import com.business_clean.app.weight.recycler.GalleryLayoutManager;
import com.business_clean.app.weight.recycler.HorizontalDecoration;
import com.business_clean.data.dao.WaterPhotoData;
import com.business_clean.data.dao.WatermarkPhotoDatabaseManager;
import com.business_clean.data.initconfig.MapInfo;
import com.business_clean.data.initconfig.TimestampEntity;
import com.business_clean.data.initconfig.WaterMarkBitmapData;
import com.business_clean.data.mode.address.AddressEntity;
import com.business_clean.data.mode.address.AddressListEntity;
import com.business_clean.data.mode.address.RegeoEntity;
import com.business_clean.data.mode.login.UserInfo;
import com.business_clean.data.mode.project.CheckClockInEntity;
import com.business_clean.data.mode.project.ProjectMangerList;
import com.business_clean.data.mode.project.ClockRadiusEntity;
import com.business_clean.data.mode.project.WorkRulesEntity;
import com.business_clean.databinding.ActivityCameraBinding;
import com.business_clean.ui.activity.StartActivity;
import com.business_clean.ui.activity.camera.controller.WaterUploadManager;
import com.business_clean.ui.activity.login.LoginActivity;
import com.business_clean.ui.activity.me.MyCenterActivity;
import com.business_clean.ui.activity.me.MyPhotosActivity;
import com.business_clean.ui.adapter.watermark.WaterClockAdapter;
import com.business_clean.viewmodel.request.CameraViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.idlefish.flutterboost.FlutterBoost;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.luck.picture.lib.interfaces.OnKeyValueResultCallbackListener;
import com.lxj.xpopup.XPopup;
import com.otaliastudios.cameraview.CameraException;
import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.CameraOptions;
import com.otaliastudios.cameraview.PictureResult;
import com.otaliastudios.cameraview.VideoResult;
import com.otaliastudios.cameraview.controls.Audio;
import com.otaliastudios.cameraview.controls.Facing;
import com.otaliastudios.cameraview.controls.Flash;
import com.otaliastudios.cameraview.controls.PictureFormat;
import com.otaliastudios.cameraview.gesture.Gesture;
import com.otaliastudios.cameraview.gesture.GestureAction;
import com.otaliastudios.cameraview.gesture.TapGestureFinder;
import com.otaliastudios.cameraview.size.Size;
import com.otaliastudios.cameraview.size.SizeSelector;
import com.zhixinhuixue.library.common.ext.DensityExtKt;

import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;
import me.hgj.mvvmhelper.ext.AppExtKt;
import me.hgj.mvvmhelper.ext.LogExtKt;
import me.hgj.mvvmhelper.net.manager.NetState;
import me.hgj.mvvmhelper.util.LogXmManager;
import rxhttp.wrapper.param.RxHttp;


import static java.lang.Math.abs;
import static me.hgj.mvvmhelper.ext.CommExtKt.isLocationOpen;

/**
 * 水印相机操作界面
 */
public class WatermarkCameraActivity extends BaseActivity<CameraViewModel, ActivityCameraBinding> implements OnLocationStatusChangeListener {
    private String TAG = "WatermarkCameraActivity";


    //旋转的角度
    private final int ORIGIN = 0;
    private final int LEFT = 1;
    private final int RIGHT = 2;
    private final int TOP = 3;
    //录制视频的最大时常
    private final int MAX_VIDEO_TIME = 1000 * 60 * 30;

    private boolean showFocuseView = true;

    private Size pictureSize;

    //来控制闪光灯 默认关闭
    private int isFlash = 0;

    //收入相册的动画，先删除倆
    private AnimatorSet setScale;

    //录制倒计时
    private Disposable mVideoTime;

    //防伪码的View
    private View securityView = null;

    //自定义Logo位置状态 0:默认位置(viewWaterMask上面) 1:左上角 2:右上角 3:屏幕中央
    private int logoPosition = 0;
    private int logoScale = 0;
    //当前屏幕方向状态，用于Logo位置计算
    private int currentOrientation = ORIGIN;

    //来判断当前是否
    private Disposable disposableLocation;
    //高德地图的api
    private LocService mLocationService = null;
    //项目的uuid
    private String project_uuid;
    //安全码
    private long takePhotoMills = 0;
    private double currentLatitude = 0.0;
    private double currentLongitude = 0.0;
    private String currentAddress = "获取定位地址中...";
    //拍照 录制的按钮
    private CaptureButton captureButton;

    private boolean isRecordShort = true;//记录是否是录制时间过短

    private TextView tvSecurityView = null;

    private boolean isActionVideo = false; //检查操作是视频还是拍照

    //TAG标记打卡的内容
    private WaterClockAdapter mAdapter = new WaterClockAdapter();

    private static final long TIME_INTERVAL = 2000; // 两次点击间的最大时间间隔
    private long backPressedTime;

    public List<String> mp4List = new ArrayList<>();

    //本地的计时器
    private Date nowLocalDate;//记录当前的时间 因为时间全靠自己来取了

    private LocationModeChangeReceiver locationModeChangeReceiver;

    /*****************本地定时器**********************/
    private Handler handlerLocTime = new Handler();
    private Runnable updateRunnable;
    private long initialServerTime = -1;
    private long initialBootTime = -1;
    /*****************本地定时器**********************/


    /*****************监听高德地图定位是否完成**********************/
    private Handler handlerBaiduLocTime = new Handler();
    private Runnable baiduLocRunnable;
    private boolean isGetBaiduLoc = false;
    /*****************监听高德地图定位是否完成**********************/

    /*****************每5秒上报一次当前的情况**********************/
    private Handler handlerUpload = new Handler();
    private Runnable handlderMonitorRunnable;
    /*****************每5秒上报一次当前的情况**********************/
    private WaterUploadManager waterUploadManager;

    ///高德地图的内容
    private AMapLocation gMapLocation;

    //Gps
    private GPSLocationManager gpsLocationManager;

    private int CHANNEL = 0;
    private String task_uuid;

    //当前界面打卡用的标签
    private List<String> tagArr = new ArrayList<>();


    @Override
    public boolean showToolBar() {
        return false;
    }

    @Override
    protected void initImmersionBar() {
        ImmersionBar.with(this)
                .statusBarColor(R.color.black)
                .navigationBarColor(R.color.black)
                .titleBar(mToolbar)
                .init();
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        LogXmManager.log("进入拍照界面 当前的app版本是 " + AppUtils.getAppVersionName());
        if (getIntent() != null && getIntent().getExtras() != null) {
            CHANNEL = getIntent().getExtras().getInt("channel");
            task_uuid = getIntent().getExtras().getString("uuid");
            project_uuid = getIntent().getExtras().getString("project_uuid");
        }


        //同步拉取地图的key
        mViewModel.requestAMapInfo();
        //如果不是任务模式才去请求配置
        if (TextUtils.isEmpty(task_uuid)) {
            //获取配置
            mViewModel.getUserCameraOne();
            //获取当前企业下所有的打卡地点
            mViewModel.requestAddressAll(project_uuid);
            //在这里重新获取用下时间
            mViewModel.requestTime();
            //获取企业的配置 设置集体打卡
            mViewModel.requestWorkRules();//获取企业项目
        }
        //检查是否需要更新
        AppCheckUpdateUtil.checkVersionNew(false, true);
        //注册定位的广播
        registerLocationModeChangeReceiver();
        //底部自定义View的设置 大小等
        initCustomCenterRoundView();
        //当前界面需要到的一些信息 本地取
        initActivityInfo();
        //根据角色操作tab的显示、延迟选中默认的tab
        initTabRecycler();
        //检查当前界面，需要用到的权限，然后进行后续内容
        checkNowActivityPermission();
        //设置当前地址的默认值
        updateWaterMaskAddress(currentAddress);
        //跟据uuid 来判断 显示的内容 这个放最后
        initGoneView();
        //查询本地是否有待上传的任务
        findLocalDbData(false);
        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            startMonitor();
        }
    }


    ///监听当前用户的界面情况
    private void startMonitor() {
        handlderMonitorRunnable = this::monitorRunnable;
        handlerUpload.postDelayed(handlderMonitorRunnable, 5000); // 每3秒检查一次是否有结果返回
    }

    private void monitorRunnable() {
        HashMap<String, String> hashMap = new HashMap<>();
        //当前是那个用户
        UserInfo userInfo = App.getAppViewModelInstance().getUserInfo().getValue();
        if (userInfo != null && userInfo.getUser() != null) {
            hashMap.put("user_name", "" + userInfo.getUser().getUser_name());
            hashMap.put("user_phone", "" + userInfo.getUser().getMobile());
        }
        //判断网络是否是移动数据
        hashMap.put("isMobileData", "" + NetworkUtils.isMobileData());
        //判断网络是否是 4G
        hashMap.put("is4G", "" + NetworkUtils.is4G());
        //获取当前网络类型
        hashMap.put("NetworkType", "" + NetworkUtils.getNetworkType());
        ///当前的Ip地址
        hashMap.put("IPAddress", "" + NetworkUtils.getIPAddress(true));
        //当前是否使手机
        hashMap.put("isPhone", "" + PhoneUtils.isPhone());
        //经纬度
        hashMap.put("latitude", "" + currentLatitude);
        hashMap.put("longitude", "" + currentLongitude);
        //如果高德返回的 内容不等于空，那么就传
        if (gMapLocation != null) {
            hashMap.put("map_error", "" + gMapLocation.getLocationDetail());
            hashMap.put("map_error_code", "" + gMapLocation.getErrorCode());
        }
        mViewModel.requestTimeUpload(hashMap);
        // 重新设置延迟，以实现循环
        if (!this.isFinishing()) {
            handlerUpload.postDelayed(handlderMonitorRunnable, 5000); // 每5秒再次执行
        }
    }

    private void initGoneView() {
        if (!TextUtils.isEmpty(task_uuid)) {
            mDatabind.llCameraCircle.setVisibility(View.GONE);
            mDatabind.llCameraWork.setVisibility(View.GONE);
            mDatabind.llCameraTodo.setVisibility(View.GONE);
            mDatabind.llCameraRoster.setVisibility(View.GONE);
            mDatabind.llChangeProject.setVisibility(View.GONE);
            mDatabind.ivMyAttendance.setVisibility(View.GONE);
            mDatabind.ivMyPhoto.setVisibility(View.GONE);
            mDatabind.recycler.setVisibility(View.GONE);
            mDatabind.tagView.setVisibility(View.GONE);
            mDatabind.ivPuzzle.setVisibility(View.GONE);
            mDatabind.ivSetting.setVisibility(View.GONE);
            mDatabind.ivBack.setVisibility(View.VISIBLE);
        } else {
            mDatabind.ivBack.setVisibility(View.GONE);
            if (!MMKVHelper.getBoolean(ConstantMMVK.IS_SEE_KQ, true)) {
                mDatabind.ivMyAttendance.setVisibility(View.GONE);
            } else {
                mDatabind.ivMyAttendance.setVisibility(View.VISIBLE);
            }
            if (!MMKVHelper.getBoolean(ConstantMMVK.IS_SEE_PHOTO, true)) {
                mDatabind.ivMyPhoto.setVisibility(View.GONE);
            } else {
                mDatabind.ivMyPhoto.setVisibility(View.VISIBLE);
            }
        }
    }

    /**
     * 当前界面需要到的一些信息 本地取 - 优化版本，确保保洁员能稳定获取项目信息
     */
    private void initActivityInfo() {
        //设置项目信息 - 多重保障机制确保保洁员能获取到项目信息
        initProjectInfoWithMultipleBackup();

        //如果是保洁员 就显示个拍照按钮 针对不同的按钮  需要隐藏
        if (Constant.ROLE_CLEANER && MMKVHelper.getBoolean(ConstantMMVK.IS_MINIMALISM, true)) {
            mDatabind.llCameraTodo.setVisibility(View.INVISIBLE);
            mDatabind.llCameraWork.setVisibility(View.INVISIBLE);
            mDatabind.llCameraRoster.setVisibility(View.INVISIBLE);
            mDatabind.llCameraCircle.setVisibility(View.INVISIBLE);
        } else {
            mDatabind.llCameraTodo.setVisibility(View.VISIBLE);
            mDatabind.llCameraWork.setVisibility(View.VISIBLE);
            mDatabind.llCameraRoster.setVisibility(View.VISIBLE);
            mDatabind.llCameraCircle.setVisibility(View.VISIBLE);
        }
        if (Constant.ROLE_CLEANER || Constant.ROLE_LEADER || Constant.ROLE_PROJECT_OWNER) {
            mDatabind.ivProject.setVisibility(View.GONE);
        }
    }

    /**
     * 多重保障机制初始化项目信息，确保保洁员能够获取到项目信息
     */
    private void initProjectInfoWithMultipleBackup() {
        ProjectMangerList finalProjectInfo = null;
        String finalProjectName = "";
        String finalProjectUuid = "";

        LogUtils.e("开始初始化项目信息 - 当前角色: " + (Constant.ROLE_CLEANER ? "保洁员" : "其他角色") +
                ", task_uuid: " + (TextUtils.isEmpty(task_uuid) ? "空" : task_uuid));

        // 1. 优先使用传入的project_uuid（如果有的话）
        if (!TextUtils.isEmpty(project_uuid)) {
            LogUtils.e("方案1: 使用传入的project_uuid: " + project_uuid);
            finalProjectUuid = project_uuid;
        }

        // 2. 尝试从全局项目信息获取
        if (App.getAppViewModelInstance().getProjectInfo().getValue() != null) {
            ProjectMangerList globalProject = App.getAppViewModelInstance().getProjectInfo().getValue();
            LogUtils.e("方案2: 从全局项目信息获取: " + globalProject.toString());

            if (!TextUtils.isEmpty(globalProject.getUuid())) {
                finalProjectInfo = globalProject;
                finalProjectUuid = globalProject.getUuid();
                finalProjectName = !TextUtils.isEmpty(globalProject.getProject_short_name()) ?
                        globalProject.getProject_short_name() : globalProject.getProject_name();
            }
        }

        // 3. 如果全局项目信息为空或无效，从本地存储获取用户信息中的项目
        if (finalProjectInfo == null || TextUtils.isEmpty(finalProjectUuid)) {
            LogUtils.e("方案3: 从本地存储获取用户信息中的项目");
            String userInfoJson = MMKVHelper.getString(ConstantMMVK.USER_INFO, "");
            if (!TextUtils.isEmpty(userInfoJson)) {
                try {
                    UserInfo userInfo = new Gson().fromJson(userInfoJson, UserInfo.class);
                    if (userInfo != null && userInfo.getProject() != null && !TextUtils.isEmpty(userInfo.getProject().getUuid())) {
                        finalProjectInfo = userInfo.getProject();
                        finalProjectUuid = userInfo.getProject().getUuid();
                        finalProjectName = !TextUtils.isEmpty(userInfo.getProject().getProject_short_name()) ?
                                userInfo.getProject().getProject_short_name() : userInfo.getProject().getProject_name();

                        // 同时更新全局项目信息
                        App.getAppViewModelInstance().getProjectInfo().setValue(finalProjectInfo);
                        LogUtils.e("从本地存储恢复项目信息并更新全局: " + finalProjectInfo.toString());
                    }
                } catch (Exception e) {
                    LogUtils.e("解析本地用户信息失败: " + e.getMessage());
                }
            }
        }

        // 4. 设置项目信息到界面
        if (finalProjectInfo != null && !TextUtils.isEmpty(finalProjectUuid)) {
            // 确保项目名称不为空
            if (TextUtils.isEmpty(finalProjectName)) {
                finalProjectName = !TextUtils.isEmpty(finalProjectInfo.getProject_name()) ?
                        finalProjectInfo.getProject_name() : "未知项目";
            }

            mDatabind.tvProject.setText(finalProjectName);
            project_uuid = finalProjectUuid;

            LogUtils.e("项目信息设置成功 - 项目名: " + finalProjectName + ", UUID: " + finalProjectUuid);
        } else {
            // 5. 所有方式都无法获取项目信息的最后处理
            LogUtils.e("警告: 无法获取项目信息，这可能导致保洁员无法打卡");
            mDatabind.tvProject.setText("获取项目信息失败");
            project_uuid = "";

            // 对于保洁员，这是严重问题，尝试重新获取用户信息
            if (Constant.ROLE_CLEANER) {
                LogUtils.e("保洁员无法获取项目信息，尝试重新获取用户信息");
                mViewModel.requestUserInfoData();

                // 显示提示信息
                ToastUtil.show("正在获取项目信息，请稍候...");
            }
        }
    }

    /**
     * 底部防微信拍照的按钮设置
     */
    private void initCustomCenterRoundView() {
        //设置录制按钮的大小
        captureButton = new CaptureButton(this);
        FrameLayout.LayoutParams btn_capture_param = new FrameLayout.LayoutParams(DensityExtKt.dp2px(76f), DensityExtKt.dp2px(76f));
        btn_capture_param.gravity = Gravity.CENTER;
        captureButton.setLayoutParams(btn_capture_param);
        mDatabind.flCapture.addView(captureButton);
        //防伪码相关的内容
        securityView = getLayoutInflater().inflate(R.layout.view_water_mask_security, null);
        tvSecurityView = securityView.findViewById(R.id.tv_mash_security);
        tvSecurityView.setText("防伪码 " + System.currentTimeMillis());
    }

    /**
     * 打卡类型 的默认设置
     */
    private void initTabRecycler() {
        if (!TextUtils.isEmpty(task_uuid)) {
            return;
        }
        tagArr.clear();
        //打卡类型
        tagArr.add("打卡");
        //如果是保洁角色，并且个人中心的工作拍照是开启的，那么就拿这个走
        if (Constant.ROLE_CLEANER || Constant.ROLE_LEADER || Constant.ROLE_PROJECT_OWNER) {
            LogUtils.e("这是本地的参数---" + MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, 0));
            if (MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, 0) == 1) {
                tagArr.add("工作拍照");
            }
        } else {
            tagArr.add("工作拍照");
        }

        if (Constant.IS_COLLECTIVE_CLOCK_IN && !Constant.ROLE_CLEANER) {
            tagArr.add("集体打卡");
        }


        //创建Adapter
        mDatabind.recycler.setAdapter(mAdapter);

        //配置
        LinearLayoutManager layoutManager = new GalleryLayoutManager(this, RecyclerView.HORIZONTAL, false);
        mDatabind.recycler.setLayoutManager(layoutManager);
        mDatabind.recycler.addItemDecoration(new HorizontalDecoration(0));

        LinearSnapHelper snapHelper = new LinearSnapHelper();
        snapHelper.attachToRecyclerView(mDatabind.recycler);

        //填充数据
        mAdapter.setList(tagArr);

        //默认选择第一个
        if (mAdapter.getData().size() > 0) {
            mAdapter.updateSingleItem(mAdapter.getData().get(0));
            mDatabind.recycler.scrollToPosition(0);
            changeFacingStyle(mAdapter.getData().get(0));
        }
        ///监听滑动事件
        final RecyclerView.OnScrollListener scrollListener = new RecyclerView.OnScrollListener() {
            private int lastCenterPosition = RecyclerView.NO_POSITION;

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                int centerPosition = findCenterViewPosition(recyclerView);
                if (centerPosition != RecyclerView.NO_POSITION && centerPosition != lastCenterPosition) {
                    lastCenterPosition = centerPosition;
                    mAdapter.updateSingleItem(mAdapter.getData().get(centerPosition));
                    changeFacingStyle(mAdapter.getData().get(centerPosition));
                }
            }

            private int findCenterViewPosition(RecyclerView recyclerView) {
                int childCount = recyclerView.getChildCount();
                if (childCount == 0) {
                    return RecyclerView.NO_POSITION;
                }

                int centerX = recyclerView.getWidth() / 2;
                int closestPosition = RecyclerView.NO_POSITION;
                int smallestDiff = Integer.MAX_VALUE;

                for (int i = 0; i < childCount; i++) {
                    View child = recyclerView.getChildAt(i);
                    int childCenterX = child.getLeft() + child.getWidth() / 2;
                    int diff = Math.abs(childCenterX - centerX);
                    if (diff < smallestDiff) {
                        smallestDiff = diff;
                        closestPosition = recyclerView.getChildAdapterPosition(child);
                    }
                }

                return closestPosition;
            }
        };

        // 先移除滚动监听器
        mDatabind.recycler.removeOnScrollListener(scrollListener);

        // 完成初始化后重新添加滚动监听器
        mDatabind.recycler.post(() -> mDatabind.recycler.addOnScrollListener(scrollListener));

        //判断是否最后一个tab
        if (mAdapter.getData().size() == 1) {
            mDatabind.recycler.setVisibility(View.GONE);
            mDatabind.tagView.setVisibility(View.GONE);
        }
    }

    /**
     * 拍照的时候确认是否需要确认
     */
    private boolean checkPicConfirm() {
        //查看本地是否开启了二次确认
        int dk = MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_SELF_CLOCK, 0);
        if (dk == 1) {
            return true;
        }
        return false;
    }

    /**
     * 有钝感力的动画
     *
     * @param position
     */
    private void smoothScrollToPosition(int position) {
        LinearSmoothScroller smoothScroller = new LinearSmoothScroller(mDatabind.recycler.getContext()) {
            @Override
            protected float calculateSpeedPerPixel(DisplayMetrics displayMetrics) {
                return 150f / displayMetrics.densityDpi; // 调整速度以达到所需的平滑效果
            }
        };
        smoothScroller.setTargetPosition(position);
        mDatabind.recycler.getLayoutManager().startSmoothScroll(smoothScroller);
    }

    /**
     * 检查当前界面需要到的权限 然后获取当前的定位模式
     */
    private void checkNowActivityPermission() {
        XXPermissions.with(this).permission(Permission.CAMERA, Permission.ACCESS_FINE_LOCATION,
                        Permission.MANAGE_EXTERNAL_STORAGE, Permission.RECORD_AUDIO)
                .request(new OnPermissionCallback() {
                    @Override
                    public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                        if (allGranted) {
                            initCamera();//初始化相机操作
                            createTimer();//创建当前的时间计时器
                            getCurrentLocation();//获取当前的定位
                        } else {
                            ToastUtil.show("使用该功能，需要使用到特定权限，请开启后再使用");
                        }
                    }
                });
    }

    /**
     * 注册当前gps的定位广播
     */
    private void registerLocationModeChangeReceiver() {
        gpsLocationManager = GPSLocationManager.getInstances(WatermarkCameraActivity.this);
        //注册
        IntentFilter filter = new IntentFilter(LocationManager.MODE_CHANGED_ACTION);
        locationModeChangeReceiver = new LocationModeChangeReceiver(this);
        registerReceiver(locationModeChangeReceiver, filter);
    }

    /**
     * 创建自己的定时器
     * APP_BACKEND_TIME 是记录进入当前界面的时间，超过5分钟，在onRestart中去重新启动App
     */
    private void createTimer() {
        //保存当前的系统的时间进来 - 这里是做清除哦，跟更新无关
        MMKVHelper.putString(ConstantMMVK.APP_BACKEND_TIME, String.valueOf(System.currentTimeMillis()));
        startUpdateTime();
    }

    /**
     * 初始化相机
     */
    private void initCamera() {
        try {
            if (mDatabind.cameraView == null) {
                LogXmManager.log("相机是空的 -- 不执行initCamera方法了");
                return;
            }
            mDatabind.cameraView.setLifecycleOwner(this);
            mDatabind.cameraView.setPictureFormat(PictureFormat.JPEG);
            mDatabind.cameraView.setPictureSize(new SizeSelector() {
                @NonNull
                @Override
                public List<Size> select(@NonNull List<Size> source) {
                    Size size = findSize(source);
                    pictureSize = size;
                    return Collections.singletonList(size);
                }
            });
            LogXmManager.log("相机setPictureSize设置完成，下一步");

            mDatabind.cameraView.setPreviewStreamSize(source -> Collections.singletonList(findSize(source)));
            LogXmManager.log("相机setPreviewStreamSize设置完成，下一步");

            mDatabind.cameraView.mapGesture(Gesture.PINCH, GestureAction.ZOOM);
            mDatabind.cameraView.mapGesture(Gesture.TAP, GestureAction.AUTO_FOCUS);
            mDatabind.cameraView.mapGesture(Gesture.LONG_TAP, GestureAction.AUTO_FOCUS);
            LogXmManager.log("相机mapGesture设置完成，下一步");

            mDatabind.cameraView.setAutoFocusResetDelay(3000);
            LogXmManager.log("相机设置完成，开始注册监听");

            mDatabind.cameraView.addCameraListener(new CameraListener() {
                @Override
                public void onCameraOpened(@NonNull CameraOptions options) {
                    super.onCameraOpened(options);
                    LogXmManager.log("相机的状态-onCameraOpened");
                    LogUtils.e("onCameraOpened");
                    toggleCamera(true);
                    if (mDatabind.cameraView.getFacing() == Facing.FRONT) {
                        // 前置摄像头处理
                    } else {
                        if (mDatabind.cameraView.isTakingVideo()) {
                            return;
                        }
                        // 后置摄像头处理
                    }
                }

                @Override
                public void onOrientationChanged(int orientation) {
                    super.onOrientationChanged(orientation);
                    LogXmManager.log("相机的状态-onOrientationChanged " + orientation);
                    LogExtKt.logE("onOrientationChanged--》" + orientation, TAG);
                    if (mDatabind.cameraView.isTakingVideo()) {
                        return;
                    }
                    switch (orientation) {
                        case 0:
                        case 180:
                            rotateText(ORIGIN);
                            break;
                        case 90:
                            rotateText(ORIGIN);
                            break;
                        case 270:
                            rotateText(LEFT);
                            break;
                    }
                }

                @Override
                public void onCameraClosed() {
                    super.onCameraClosed();
                    LogXmManager.log("相机的状态-onCameraClosed");
                    toggleCamera(false);
                }

                @Override
                public void onCameraError(@NonNull CameraException exception) {
                    super.onCameraError(exception);
                    toggleCamera(false);
                    LogXmManager.log("onCameraError " + exception.getMessage());
                    LogUtils.e("相机报错了 " + exception.getMessage());
                }

                @Override
                public void onPictureTaken(@NonNull PictureResult result) {
                    super.onPictureTaken(result);
                    LogXmManager.log("相机的状态-onPictureTaken");
                    if (nowLocalDate != null) {
                        takePhotoMills = TimeUtils.date2Millis(nowLocalDate);
                    } else {
                        takePhotoMills = System.currentTimeMillis();
                    }
                    if (tvSecurityView != null && takePhotoMills > 0) {
                        tvSecurityView.setText("防伪码 " + takePhotoMills);
                    }
                    vibratePhone();
                    actionOperation(true, result, null);
                }

                @Override
                public void onAutoFocusStart(@NonNull PointF point) {
                    super.onAutoFocusStart(point);
                    LogXmManager.log("相机的状态-onAutoFocusStart");
                    mDatabind.cfvView.setTranslationX(point.x - SizeUtils.dp2px(40));
                    mDatabind.cfvView.setTranslationY(point.y - SizeUtils.dp2px(40));
                    mDatabind.cfvView.setVisibility(View.VISIBLE);
                    mDatabind.cfvView.setIslongClick(isLongClick());
                    if (showFocuseView) {
                        mDatabind.cfvView.setVisibility(View.VISIBLE);
                    } else {
                        mDatabind.cfvView.setVisibility(View.GONE);
                    }
                }

                @Override
                public void onAutoFocusEnd(boolean successful, @NonNull PointF point) {
                    super.onAutoFocusEnd(successful, point);
                    LogXmManager.log("相机的状态-onAutoFocusEnd");
                    if (isLongClick() && showFocuseView) {
                        LogExtKt.logE("相机的状态-onAutoFocusEnd", TAG);
                    } else {
                        mDatabind.cfvView.animate().setDuration(300).setStartDelay(1000).setListener(new AnimatorListenerAdapter() {
                            @Override
                            public void onAnimationEnd(Animator animation) {
                                super.onAnimationEnd(animation);
                                mDatabind.cfvView.setVisibility(View.GONE);
                            }
                        }).start();
                    }
                }

                @Override
                public void onVideoRecordingStart() {
                    super.onVideoRecordingStart();
                    LogXmManager.log("相机的状态-onVideoRecordingStart");
                }

                @Override
                public void onVideoRecordingEnd() {
                    super.onVideoRecordingEnd();
                    LogXmManager.log("相机的状态-onVideoRecordingEnd");
                }

                @Override
                public void onVideoTaken(@NonNull VideoResult result) {
                    super.onVideoTaken(result);
                    LogXmManager.log("相机的状态-onVideoTaken");
                    LogExtKt.logE("onVideoTaken-->" + result.getFile().getAbsoluteFile(), TAG);
                    if (!isRecordShort) {
                        if (nowLocalDate != null) {
                            takePhotoMills = TimeUtils.date2Millis(nowLocalDate);
                        } else {
                            takePhotoMills = System.currentTimeMillis();
                        }
                        vibratePhone();
                        actionOperation(false, null, result.getFile().getAbsolutePath());
                    }
                }

                @Override
                public void onZoomChanged(float newValue, @NonNull float[] bounds, @androidx.annotation.Nullable PointF[] fingers) {
                    super.onZoomChanged(newValue, bounds, fingers);
                    LogXmManager.log("相机的状态-onZoomChanged");
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            LogXmManager.log("相机初始化报错了-" + e.getMessage());
        }
    }


    /**
     * 拍照、录制视频后整体的处理
     * isPic true 是图片 false 是视频
     */
    private void actionOperation(boolean isPic, PictureResult result, String videoFilePath) {
        if (!TextUtils.isEmpty(task_uuid)) {//如果有taskuuid 说明是有从任务追加照片过来的
            handlerTaskOperation(isPic, result, videoFilePath);
            return;
        }
        if (!isLocationOpen(WatermarkCameraActivity.this)) {
            CommonUtils.showGeneralDialog(WatermarkCameraActivity.this, "温馨提示", "您手机未开启定位功能", "关闭", "如何开启？", null, new OnDialogConfirmListener() {
                @Override
                public void onConfirm() {
                }
            });
        } else {
            if (!isPic) {
                if (!mAdapter.getText().equals("打卡")) {
                    JumpEditPhoto(false, null, null, null, videoFilePath);
                } else {
                    mp4List.add(videoFilePath);
                    mergeMp4List();
                }
            } else {
                if (!mAdapter.getText().equals("打卡")) {
                    JumpEditPhoto(true, compositeBitmap(result.getData(), result.getRotation(), result.getFacing()), ImageUtils.view2Bitmap(mDatabind.viewWaterMask), ImageUtils.view2Bitmap(securityView), null);
                } else {
                    //改版 如果是照片先去校验，是否可以打卡
                    uploadMedia(true, result, videoFilePath);
                }
            }
        }
    }

    /* 上传图片的操作
     * message_type 区分是图片还是视频
     * 还可以在这里处理其他的
     * 比如说办结工单、创建工单
     */
    private void uploadMedia(boolean isPic, PictureResult result, String videoFilePath) {
        if (!AppExtKt.isAvailable(this)) {
            handleUpload(isPic, result, videoFilePath);
            return;
        }

        if (!checkPicConfirm()) {
            // 压缩图片进行 db 存储，只有保洁才会走这里
            handlerClearClockResult(isPic, result, videoFilePath);
        } else {
            // 进行网络请求
            performCheckClock(isPic, result, videoFilePath);
        }
    }

    /**
     * 针对不同的角色来做跳转处理
     */
    private void handleUpload(boolean isPic, PictureResult result, String videoFilePath) {
        if (!checkPicConfirm()) {
            // 压缩图片进行 db 存储，只有保洁才会走这里
            handlerClearClockResult(isPic, result, videoFilePath);
        } else {
            JumpEditPhoto(isPic,
                    isPic ? compositeBitmap(result.getData(), result.getRotation(), result.getFacing()) : null,
                    isPic ? ImageUtils.view2Bitmap(mDatabind.viewWaterMask) : null,
                    isPic ? ImageUtils.view2Bitmap(securityView) : null,
                    isPic ? null : videoFilePath);
        }
    }

    private void performCheckClock(boolean isPic, PictureResult result, String videoFilePath) {
        RxHttp.get(NetUrl.CHECK_CLOCK_ADDRESS)
                .add("lnt", String.valueOf(currentLongitude))
                .add("lat", String.valueOf(currentLatitude))
                .add("map_code", "gao_de")
                .asResponse(CheckClockInEntity.class)
                .timeout(3, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<CheckClockInEntity>() {
                    @Override
                    public void accept(CheckClockInEntity response) throws Exception {
                        if (!TextUtils.isEmpty(response.getMessage())) {
                            getCurrentLocation();
                            showErrorDialog(response.getMessage());
                            return;
                        }
                        handleUpload(isPic, result, videoFilePath);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) {
                        // 处理错误
                    }
                });
    }

    private void showErrorDialog(String message) {
        CommonUtils.showGeneralDialogNoCancel(WatermarkCameraActivity.this,
                "温馨提示", message, "知道了", new OnDialogConfirmListener() {
                    @Override
                    public void onConfirm() {
                        // 确认操作
                    }
                });
    }

    /**
     * 保洁如果没开启二次打卡，那么就走这里进行上传操作
     *
     * @param isPic
     * @param result
     * @param videoFilePath
     */
    private void handlerClearClockResult(boolean isPic, PictureResult result, String videoFilePath) {
        handlerTaskOperation(isPic, result, videoFilePath);//因为这里是写好的，所以直接走这里就行了
    }

    private void handlerCleanClockFinalResult(int message_type, String waterFile, String original, String thumb) {
        //1打卡 2领班打卡 3日常
        int actType = 1;
        if (mAdapter.getText().equals("工作拍照")) {
            actType = 3;
        } else if (mAdapter.getText().equals("集体打卡")) {
            actType = 2;
        }
        int finalActType = actType;
        mDatabind.progress.setVisibility(View.VISIBLE);
        ServerReporter.getInstance().reportToServer(project_uuid, actType, message_type, currentLatitude, currentLongitude, currentAddress,
                waterFile, original, thumb, null, takePhotoMills, new ServerReporter.OnUploadWorkFileListener() {
                    @Override
                    public void onUploadSuccess() {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                showUploadViewResult("上传成功");
                                if (finalActType == 1) {
                                    showCleanClockSuccess(true);
                                }
                            }
                        });
                    }

                    @Override
                    public void onUploadFailed(String error) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                mDatabind.progress.setProgress(0);
                                showUploadViewResult(null);
                                CommonUtils.showGeneralDialogNoCancel(WatermarkCameraActivity.this, "温馨提示", error, "知道了", new OnDialogConfirmListener() {
                                    @Override
                                    public void onConfirm() {
                                    }
                                });
                            }
                        });
                    }

                    @Override
                    public void onUploadProgress(int progress) {
                        mDatabind.progress.setProgress(progress);
                    }
                });
    }

    /**
     * 跳转编辑图片界面
     *
     * @param isPic
     * @param compositeBitmap
     * @param view2Bitmap
     * @param view2Bitmap1
     * @param video_path
     */
    private void JumpEditPhoto(boolean isPic, Bitmap compositeBitmap, Bitmap view2Bitmap, Bitmap view2Bitmap1, String video_path) {
        WaterMarkBitmapData markBitmapData = new WaterMarkBitmapData();
        //设置原图
        markBitmapData.setOriginalBitmap(compositeBitmap);
        //设置水印图
        markBitmapData.setWaterBitmap(view2Bitmap);
        //设置防伪码的图
        markBitmapData.setKeyBitmap(view2Bitmap1);
        //设置自定义的logo
        markBitmapData.setCustomLogoBitmap(ImageUtils.view2Bitmap(mDatabind.ivCustomLogo));
        //设置位置
        markBitmapData.setCustomPos(logoPosition);
        //经纬度 地址
        markBitmapData.setLat(currentLatitude);
        markBitmapData.setLnt(currentLongitude);
        markBitmapData.setAddress(currentAddress);
        //给共同的数据，因为通过bundle 怕图片过大
        App.getAppViewModelInstance().getWaterMarkBitmap().setValue(markBitmapData);
        Bundle bundle = new Bundle();
        bundle.putInt("channel", isPic ? 1 : 2);
        bundle.putString("takePhotoMills", "" + takePhotoMills);
        bundle.putString("video_path", video_path);
        bundle.putString("act_type", mAdapter.getText());
        bundle.putString("project_uuid", project_uuid);
        ActivityForwardUtil.startActivity(EditPhotoActivity.class, bundle);
    }

    /**
     * 处理任务的情况
     */
    private void handlerTaskOperation(boolean isPic, PictureResult result, String videoFilePath) {
        if (isPic) {//这是图片
            //byte合成bitmap
            Bitmap originalBitmap = compositeBitmap(result.getData(), result.getRotation(), result.getFacing());
            //缓存 提高绘图速度
            mDatabind.viewWaterMask.setDrawingCacheEnabled(true);

            //合成水印照片
            Bitmap waterMarkBitmap = PhotoBitmapUtils.drawWaterToBitMap2(WatermarkCameraActivity.this,
                    mDatabind.viewWaterMask.getDrawingCache(), originalBitmap, ImageUtils.view2Bitmap(securityView), ImageUtils.view2Bitmap(mDatabind.ivCustomLogo), logoPosition);

            //清除水印的缓存
            mDatabind.viewWaterMask.destroyDrawingCache();

            //先存到本地项目
            File file = saveBitmapToFile(waterMarkBitmap, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + ".jpg");//水印拿到一个暂存的路径
            File originaFile = saveBitmapToFile(originalBitmap, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "origina" + ".jpg");//原图拿到一个暂存的路径
            File thumbFile = saveBitmapToFile(PhotoBitmapUtils.getBitmapThumb(file.getAbsolutePath(), 200), Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "thumb" + ".jpg");
            //先压缩水印图片
            ImageCompressor.compressBitmap(this, file.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                @Override
                public void onCallback(String srcPath, String resultWaterPath) {
                    //原图的压缩 压缩完成后 再去上传服务器，因为服务器要原图跟水印图，所以都需要压缩后 处理
                    ImageCompressor.compressBitmap(WatermarkCameraActivity.this,
                            originaFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                                @Override
                                public void onCallback(String srcPath, String resultPath) {
                                    LogUtils.e("压缩完成后的图片 resultPath -->" + resultPath);
                                    ImageCompressor.compressBitmap(WatermarkCameraActivity.this,
                                            thumbFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                                                @Override
                                                public void onCallback(String srcPath, String thumbPath) {
                                                    LogUtils.e("压缩完成后的图片 thumbFile -->" + thumbPath);
                                                    //拿到压缩后的路径了，那么就进行存储
                                                    if (Constant.CAMERA_SAVE_PHOTO_VIDEO) {//开启本地照片存储 需要保存水印照片
                                                        ImageUtils.save2Album(BitmapFactory.decodeFile(resultWaterPath), Constant.EXTERNAL_PHOTO_PATH, Bitmap.CompressFormat.JPEG);
                                                        //是否需要保存原始图片到相册
                                                        if (Constant.CAMERA_SAVE_ORIGINAL_PHOTO) {
                                                            ImageUtils.save2Album(BitmapFactory.decodeFile(resultPath), Bitmap.CompressFormat.JPEG);
                                                        }
                                                    }
                                                    uploadTaskMedia(1, resultWaterPath, resultPath, thumbPath);
                                                }
                                            });
                                }
                            });

                }
            });
        } else {
            mp4List.add(videoFilePath);
            mergeMp4List();
        }
    }


    private File saveBitmapToFile(Bitmap waterMarkBitmap, String savePath, String finalFileName) {
        File file = new File(savePath + finalFileName);
        ImageUtils.save(waterMarkBitmap, savePath + finalFileName, Bitmap.CompressFormat.JPEG);
        return file;
    }

    private void uploadTaskMedia(int message_type, String waterFile, String original, String thumb) {
        if (TextUtils.isEmpty(task_uuid)) {//因为要复用代码，所以如果是保洁的情况，并且没打开二次确认的情况下走这里
            handlerCleanClockFinalResult(message_type, waterFile, original, thumb);
            return;
        }
        LoadingDialogExtKt.showLoadingExt(WatermarkCameraActivity.this, "上传中");
        UploadFileHelper.getInstance().uploadPictures(WatermarkCameraActivity.this, waterFile, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
            @Override
            public void onUploadSuccess(String waterResponse) {
                if (2 == message_type) {
                    UploadFileHelper.getInstance().uploadPictures(WatermarkCameraActivity.this, thumb, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                        @Override
                        public void onUploadSuccess(String thumbResponse) {
                            // 分别上传水印图片和原图成功后，再发送请求
                            handlerFinalUploadResult(2, waterResponse, waterResponse, thumbResponse);
                        }

                        @Override
                        public void onUploadFailed(String error) {
                            LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
                        }

                        @Override
                        public void onUploadProgress(int progress) {
                            // 原图上传进度
                        }
                    });
                } else {
                    UploadFileHelper.getInstance().uploadPictures(WatermarkCameraActivity.this, original, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                        @Override
                        public void onUploadSuccess(String originalResponse) {
                            Log.e(TAG, "上传之前的" + originalResponse);
                            UploadFileHelper.getInstance().uploadPictures(WatermarkCameraActivity.this, thumb, UploadFileHelper.PATH_HEADER_CAMERA, new UploadFileHelper.UploadListener() {
                                @Override
                                public void onUploadSuccess(String thumbResponse) {
                                    // 分别上传水印图片和原图成功后，再发送请求
                                    handlerFinalUploadResult(1, waterResponse, originalResponse, thumbResponse);
                                }

                                @Override
                                public void onUploadFailed(String error) {
                                    LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
                                }

                                @Override
                                public void onUploadProgress(int progress) {
                                    // 原图上传进度
                                }
                            });
                        }

                        @Override
                        public void onUploadFailed(String error) {
                            LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
                        }

                        @Override
                        public void onUploadProgress(int progress) {
                            // 原图上传进度
                        }
                    });
                }
            }

            @Override
            public void onUploadFailed(String error) {
                LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
            }

            @Override
            public void onUploadProgress(int progress) {
                // 水印图片上传进度
            }
        });
    }

    /**
     * 拍照/视频来上传任务ID的，清洁计划、保洁计划、培训计划的照片等
     *
     * @param message_type
     * @param waterFile
     * @param original
     * @param thumb
     */
    private void handlerFinalUploadResult(int message_type, String waterFile, String original, String thumb) {
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("uuid", task_uuid);
        hashMap.put("lnt", "" + currentLongitude);
        hashMap.put("lat", "" + currentLatitude);
        hashMap.put("media_type", "" + message_type);//图片资源类型 1图片 2视频
        hashMap.put("media_url", waterFile);//视频封面/图片缩略图
        hashMap.put("pic_thumb", thumb);//媒体地址(有水印)
        hashMap.put("origin_media_url", original);//消息媒体地址(无水印)
        hashMap.put("security_code", "" + takePhotoMills);//防伪码
        hashMap.put("address", currentAddress);//地址
        hashMap.put("take_photo_time", "" + (takePhotoMills / 1000));//拍照时间
        hashMap.put("map_code", "gao_de");
        RxHttp.get(NetUrl.GET_TASK_ADD_PIC)
                .addAll(hashMap)
                .asResponse(Object.class)
                .timeout(3, TimeUnit.SECONDS)  //设置总超时时间为5s，在toObservableXxx方法后调用
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<Object>() {
                    @Override
                    public void accept(Object response) throws Exception {
                        LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
                        App.getAppViewModelInstance().getRefreshWorkTask().setValue(true);
                        finishActivityAll();
                        FlutterBoost.instance().sendEventToFlutter("refresh", null);//刷新当前界面
                        WatermarkCameraActivity.this.finish();
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        LoadingDialogExtKt.dismissLoadingExt(WatermarkCameraActivity.this);
                    }
                });
    }

    /**
     * 合并Mp4
     */
    public void mergeMp4List() {
        String savePath = Constant.CAMERA_SAVE_PHOTO_VIDEO ? Constant.EXTERNAL_PHOTO_PATH : Constant.INTERNAL_VIDEO_PATH;
        Mp4Utils.mergeMp4List(mp4List, new File(savePath + "/" + System.currentTimeMillis() + "mergeVideo.mp4"))
                .compose(RxHelper.rxSchedulerHelper()).subscribe(new Consumer<File>() {
                    @Override
                    public void accept(File mergeVideo) throws Exception {
                        if (Constant.CAMERA_SAVE_PHOTO_VIDEO) {
                            Mp4Utils.saveVideoToAlbum(WatermarkCameraActivity.this, mergeVideo.getAbsolutePath());
                            FileUtils.notifySystemToScan(mergeVideo);
                        }
                        mp4List.clear();
                        LogExtKt.logE("合并了完成了 " + mergeVideo.getAbsolutePath(), TAG);
                        if (TextUtils.isEmpty(task_uuid)) {//如果taskUUiD等于空，那么就走本界面的逻辑，不再走直接上传视频的操作
                            //上传视频
                            uploadMedia(false, null, mergeVideo.getAbsolutePath());
                        } else {
                            //上传视频
                            Bitmap thumb = PhotoBitmapUtils.getVideoThumbnail(mergeVideo.getAbsolutePath(), 90, 160, MediaStore.Video.Thumbnails.MINI_KIND);
                            File thumbFile = PhotoBitmapUtils.saveBitmapToFile(thumb, Constant.INTERNAL_PHOTO_PATH, System.currentTimeMillis() + "video_thumb" + ".jpg");
                            //先压缩缩略图，拿到缩略图的大小
                            ImageCompressor.compressBitmap(WatermarkCameraActivity.this,
                                    thumbFile.getAbsolutePath(), 10, new OnKeyValueResultCallbackListener() {
                                        @Override
                                        public void onCallback(String srcPath, String thumbPath) {
                                            LogUtils.e("压缩完成后的图片 resultPath -->" + thumbPath);
                                            uploadTaskMedia(2, mergeVideo.getAbsolutePath(), null, thumbPath);
                                        }
                                    });

                        }
                    }
                }, throwable -> {
                    throwable.printStackTrace();
                    LogExtKt.logE("合并出现异常" + throwable.getMessage(), TAG);
                    switchVideoMode(false);
                });
    }


    @Override
    public void initObserver() {
        //监听tag
        App.getAppViewModelInstance().getRefreshWaterTag().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                checkWorkCamera();
            }
        });
        //监听是否切换项目
        App.getAppViewModelInstance().getProjectInfo().observe(this, new Observer<ProjectMangerList>() {
            @Override
            public void onChanged(ProjectMangerList projectMangerList) {
                if (!TextUtils.isEmpty(projectMangerList.getProject_short_name())) {
                    mDatabind.tvProject.setText(projectMangerList.getProject_short_name());
                }
                project_uuid = projectMangerList.getUuid();
                mViewModel.requestWorkRules();//获取企业项目
                mViewModel.requestAddressAll(projectMangerList.getUuid());//重新获取地址
            }
        });

        //监听编辑图片回调
        App.getAppViewModelInstance().getRefreshUploadPhoto().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                findLocalDbData(aBoolean);
            }
        });
    }


    /**
     * 检查是否开启工作拍照
     */
    private void checkWorkCamera() {

        int isOpenWorkCamera = MMKVHelper.getInt(ConstantMMVK.CLEAN_OPEN_WORK_CAMERA, 0);
        LogExtKt.logE("这是本地的参数--- " + isOpenWorkCamera, TAG);
        if (isOpenWorkCamera == 1) {
            mAdapter.addData("工作拍照");
            mAdapter.notifyDataSetChanged();
        } else {
            for (int i = 0; i < mAdapter.getData().size(); i++) {
                if (mAdapter.getData().get(i).equals("工作拍照")) {
                    mAdapter.remove("工作拍照");
                }
            }
            mAdapter.notifyDataSetChanged();
        }

        if (mAdapter.getData().size() == 1) {
            mDatabind.recycler.setVisibility(View.GONE);
            mDatabind.tagView.setVisibility(View.GONE);
        } else {
            mDatabind.recycler.setVisibility(View.VISIBLE);
            mDatabind.tagView.setVisibility(View.VISIBLE);
            mDatabind.recycler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    //默认选择第一个
                    if (mAdapter.getData().size() > 0) {
                        mAdapter.updateSingleItem(mAdapter.getData().get(0));
                        mDatabind.recycler.scrollToPosition(0);
                        changeFacingStyle(mAdapter.getData().get(0));
                    }
                }
            }, 300);
        }


    }


    /**
     * 查询本地数据，看本地是否有数据，进行查询
     *
     * @param
     */
    private void findLocalDbData(boolean isClock) {
        LogUtils.e("查询本地数据进行上传---" + isClock);
        mDatabind.progress.setProgress(0);
        // 初始化 WaterUploadManager
        if (waterUploadManager == null) {
            waterUploadManager = new WaterUploadManager(this);
            waterUploadManager.setOnAllUploadsCompleteListener(() -> {
                LogUtils.e("回调这里了 onAllUploadsComplete - " + isClock);
            });

            waterUploadManager.setOnSingleUploadFailedListener((data, error) -> {
                LogUtils.e("回调这里了 onSingleUploadFailed");
                mDatabind.progress.setProgress(0);
                showUploadViewResult(null);
                CommonUtils.showGeneralDialogNoCancel(WatermarkCameraActivity.this, "温馨提示", error, "知道了", new OnDialogConfirmListener() {
                    @Override
                    public void onConfirm() {
                    }
                });
            });

            waterUploadManager.setOnSingleUploadCompleteListener(data -> {
                LogUtils.e("回调这里了 onSingleUploadComplete " + data.getAct_type());
                //说明成功了
                showUploadViewResult("上传成功");
                if (1 == data.getAct_type() || 2 == data.getAct_type()) {//就是打卡
                    showCleanClockSuccess(true);
                }
            });

            waterUploadManager.setOnUploadProgressListener((data, progress) -> {
                LogUtils.e("回调这里了 onUploadProgress");
                mDatabind.progress.setVisibility(View.VISIBLE);
                mDatabind.progress.setProgress(progress);
            });
        }
        //查询是否有待上传的，然后执行任务
        List<WaterPhotoData> dataList = WatermarkPhotoDatabaseManager.getInstance(this).queryAll();
        for (WaterPhotoData data : dataList) {
            if (0 == data.getUpload_status()) {
                waterUploadManager.addUploadTask(data);
            }
        }
    }

    /**
     * 因为有好多的地方用，所以直接写一起
     */
    private void showCleanClockSuccess(boolean isClock) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (isClock) {//如果是打卡，那么显示打卡成功
                    new XPopup.Builder(WatermarkCameraActivity.this)
                            .asCustom(new CustomClockInDialog(WatermarkCameraActivity.this))
                            .show();
                }
                mDatabind.progress.setVisibility(View.GONE);
                App.getAppViewModelInstance().getRefreshCircle().setValue(true);
            }
        });
    }

    /**
     * 显示上传成功，并且1秒之后消失
     */
    private void showUploadViewResult(String text) {
        mDatabind.progress.setProgress(0);
        if (!TextUtils.isEmpty(text)) {
            mDatabind.tvUploadTips.setText(text);
            mDatabind.progress.setVisibility(View.GONE);
            mDatabind.rlUploadResultLayout.setVisibility(View.VISIBLE);
            mDatabind.rlUploadResultLayout.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mDatabind.rlUploadResultLayout.setVisibility(View.GONE);
                }
            }, 1500);
        } else {
            mDatabind.rlUploadResultLayout.setVisibility(View.GONE);
        }
    }

    /**
     * 获取当前定位
     */
    private void getCurrentLocation() {
        stopLocationDisposable();
        // 在这里执行网络请求操作，比如检查网络是否可用
        disposableLocation = Observable.interval(0, 5 * 60 * 1000, TimeUnit.MILLISECONDS)
                .flatMap(aLong -> Observable.fromCallable(NetworkUtils::isAvailable))
                .subscribeOn(Schedulers.io())  // 在 IO 线程执行网络请求
                .observeOn(AndroidSchedulers.mainThread())  // 在主线程处理结果
                .subscribe(isNetworkAvailable -> {
                    LogExtKt.logE("来高德定位了", TAG);
                    LogXmManager.log("来高德定位了 " + TAG);
                    mLocationService = new LocService(this);
                    mLocationService.registerListener(aMapLocationListener);
                    mLocationService.setLocationOption(mLocationService.getDefaultLocationClientOption());
                    mLocationService.start();
                    LogExtKt.logE("有权限了，来进行高德地图定位了版本 - ", TAG);
                    LogXmManager.log("有权限了，来进行高德地图定位了版本 - " + TAG);
                    startHandlerBaidu();
                });

    }

    private void startHandlerBaidu() {
        stopUpdateBaiduTime();
        isGetBaiduLoc = false;
        baiduLocRunnable = this::updateCheckBaidu;
        handlerBaiduLocTime.postDelayed(baiduLocRunnable, 60000); // 每3秒检查一次是否有结果返回
    }

    private void updateCheckBaidu() {
        if (this.isFinishing()) {
            stopUpdateBaiduTime();
            stopLocationDisposable();
            return;
        }
        LogExtKt.logE("高德地图来检查了状态了isGetBaiduLoc  -  " + isGetBaiduLoc, TAG);
        LogXmManager.log("高德地图来检查了 isGetBaiduLoc - " + isGetBaiduLoc);

        if (!isGetBaiduLoc && gpsLocationManager != null) {
            LogXmManager.log("高德地图没响应，来原生Gps定位了 " + AppExtKt.isAvailable(this));

            if (gpsLocationManager != null) {
                gpsLocationManager.start(new MyListener());
            }
            handlerBaiduLocTime.removeCallbacks(baiduLocRunnable);
        }
        //等于true 说明定位成功了，暂停轮训
        if (isGetBaiduLoc) {
            if (mLocationService != null) {
                mLocationService.stop();
            }
            stopUpdateBaiduTime();
            LogXmManager.log("定位成功了,暂停轮训");
            LogExtKt.logE("定位成功了,暂停轮训 ", TAG);
        }
    }

    /**
     * 更换摄像头 前后
     *
     * @param text
     */
    private void changeFacingStyle(String text) {
        if ("打卡".equals(text)) {
            mDatabind.cameraView.setFacing(Facing.FRONT);
        } else {
            mDatabind.cameraView.setFacing(Facing.BACK);
        }
    }


    @Override
    public void onBindViewClick() {
        closeCamera(mDatabind.llCameraWork, 0);
        closeCamera(mDatabind.llCameraTodo, 1);
        closeCamera(mDatabind.llCameraRoster, 3);
        closeCamera(mDatabind.llCameraCircle, 4);
        //关闭界面
        mDatabind.ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finishActivityAll();
                WatermarkCameraActivity.this.finish();
            }
        });
        //我的考勤
        mDatabind.ivMyAttendance.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getUser() != null) {
                    CommonToFlutter.gotoFlutterAttendanceStatPage(App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUuid(),
                            App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUser_name());
                }
            }
        });
        //我的相册
        mDatabind.ivMyPhoto.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityForwardUtil.startActivity(MyPhotosActivity.class);
            }
        });
        //拼图
        mDatabind.ivPuzzle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                HashMap<String, Object> hashMap = new HashMap<>();
                if (App.getAppViewModelInstance() != null && App.getAppViewModelInstance().getUserInfo().getValue() != null && App.getAppViewModelInstance().getUserInfo().getValue().getUser() != null) {
                    hashMap.put("reporter_name", "" + App.getAppViewModelInstance().getUserInfo().getValue().getUser().getUser_name());
                }
                FlutterBoost.instance().open(new FlutterBoostRouteOptions.Builder()
                        .pageName("jigsawPuzzlePage")
                        .arguments(hashMap)
                        .build());
            }
        });
        //切换摄像头
        mDatabind.ivChangeCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mDatabind.cameraView.toggleFacing();
            }
        });
        //闪光灯
        mDatabind.ivFlash.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isFlash == 0) {
                    isFlash = 1;
                    mDatabind.cameraView.setFlash(Flash.TORCH);
                    mDatabind.ivFlash.setImageResource(R.mipmap.icon_camera_flash_open);
                } else if (isFlash == 1) {
                    isFlash = 2;
                    mDatabind.cameraView.setFlash(Flash.AUTO);
                    mDatabind.ivFlash.setImageResource(R.mipmap.icon_camera_auto_flash);
                } else {
                    isFlash = 0;
                    mDatabind.cameraView.setFlash(Flash.OFF);
                    mDatabind.ivFlash.setImageResource(R.mipmap.icon_camera_flash);
                }
            }
        });

        //打卡方式
        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                changeFacingStyle(mAdapter.getData().get(position));
                mAdapter.updateSingleItem(mAdapter.getData().get(position));
                mDatabind.recycler.smoothScrollToPosition(position);
            }
        });
        //切换项目
        mDatabind.llChangeProject.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Constant.ROLE_CLEANER || Constant.ROLE_LEADER || Constant.ROLE_PROJECT_OWNER) {
                    return;
                }
                CommonUtils.showSelectProjectDialog(WatermarkCameraActivity.this, project_uuid, true, false, true, new PagerDrawerPopup.OnSelectProjectListener() {

                    @Override
                    public void onClick(String projectUuid, String project_name) {
                        if (!TextUtils.isEmpty(project_name)) {
                            mDatabind.tvProject.setText(project_name);
                        }
                        project_uuid = projectUuid;
                        mViewModel.requestWorkRules();//获取企业项目
                        mViewModel.requestAddressAll(project_uuid);//重新获取地址
                    }
                });
            }
        });

        //按钮的长按
        captureButton.setCaptureLisenter(new CaptureListener() {
            @Override
            public void onTick(long time) {
                LogUtils.e("" + time);
            }

            @Override
            public void takePictures() {
                isActionVideo = false;

                if (!isLocationOpen(WatermarkCameraActivity.this)) {
                    new XPopup.Builder(WatermarkCameraActivity.this)
                            .asCustom(new CustomLocationDialog(WatermarkCameraActivity.this))
                            .show();
                    return;
                }

                if (currentLatitude == 0.0 || currentLongitude == 0.0) {
                    CommonUtils.showGeneralDialogNoCancel(WatermarkCameraActivity.this, "温馨提示", "没有获取到定位地址，无法打卡", "知道了", new OnDialogConfirmListener() {
                        @Override
                        public void onConfirm() {
                            getCurrentLocation();//重新获取高德的地址信息
                        }
                    });
                    return;
                }

                mDatabind.cameraView.takePicture();
            }

            @Override
            public void recordShort(long l) {
                isRecordShort = true;
                ToastUtil.show("录制时间过短");
                mDatabind.cameraView.stopVideo();
                switchVideoMode(true);
                mDatabind.clTime.setVisibility(View.GONE);
                if (mVideoTime != null) {
                    mVideoTime.dispose();
                }
            }

            @Override
            public void recordStart() {
                isActionVideo = false;
                if (currentLongitude == 0.0 || currentLatitude == 0.0) {
                    CommonUtils.showGeneralDialogNoCancel(WatermarkCameraActivity.this, "温馨提示", "没有获取到定位地址，无法打卡", "知道了", null);
                    captureButton.recordEnd();
                    return;
                }
                starTakeVideo();
            }

            @Override
            public void recordEnd(long l) {
                isRecordShort = false;
                if (mDatabind.cameraView.isTakingVideo()) {
                    mDatabind.cameraView.stopVideo();
                    switchVideoMode(true);
                    mDatabind.clTime.setVisibility(View.GONE);
                    if (mVideoTime != null) {
                        mVideoTime.dispose();
                    }
                    ToastUtil.show("视频录制完成");
                }
            }

            @Override
            public void recordZoom(float zoom) {

            }

            @Override
            public void recordError() {
                if (mVideoTime != null) {
                    mVideoTime.dispose();
                }
            }
        });

        //相机的设置
        mDatabind.ivSetting.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ActivityUtils.startActivity(MyCenterActivity.class);
            }
        });

        //水印地址
        mDatabind.viewWaterMask.getTvAddress().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //判断下是否开启定位权限 如果没开启，就提示开启
                if (!XXPermissions.isGranted(WatermarkCameraActivity.this, Permission.ACCESS_FINE_LOCATION)) {
                    XXPermissions.with(WatermarkCameraActivity.this)
                            .permission(Permission.ACCESS_FINE_LOCATION)
                            .request(new OnPermissionCallback() {
                                @Override
                                public void onGranted(@NonNull List<String> permissions, boolean allGranted) {
                                    if (allGranted) {
                                        getCurrentLocation();
                                    }
                                }
                            });
                }
            }
        });

    }


    /**
     * 启动相机
     */
    private void starTakeVideo() {
        switchVideoMode(false);
        mDatabind.tvMashSecurity.setText("防伪码:" + TimeUtils.getNowMills());
        mDatabind.clTime.setVisibility(View.VISIBLE);
        mDatabind.tvTime.setText("0:00");
        mDatabind.cameraView.setAudio(Audio.ON);
        File file = new File(getCacheDir(), System.currentTimeMillis() + ".mp4");
        mDatabind.cameraView.takeVideoSnapshot(file, MAX_VIDEO_TIME);
        if (mVideoTime != null) {
            mVideoTime.dispose();
        }
        mVideoTime = Observable.interval(0, 1, TimeUnit.SECONDS).observeOn(AndroidSchedulers.mainThread()).subscribe(new Consumer<Long>() {
            @Override
            public void accept(Long seconds) throws Exception {
                mDatabind.tvTime.setText(formatTime(seconds));
            }
        });
    }


    public boolean isLongClick() {
        try {
            for (Field field : mDatabind.cameraView.getClass().getDeclaredFields()) {
                if (field.getType() == TapGestureFinder.class) {
                    field.setAccessible(true);
                    TapGestureFinder tapGestureFinder = (TapGestureFinder) field.get(mDatabind.cameraView);
                    return tapGestureFinder.getGesture() == Gesture.LONG_TAP;
                }
            }
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 这里是完成图片
     */
    private Bitmap compositeBitmap(byte[] bytes, int rotation, Facing facing) {
        //byte合成bitmap
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);
        //旋转图片的角度
        originalBitmap = PhotoBitmapUtils.rotateBitmap(originalBitmap, rotation, true);
        //如果是前置摄像头拍的,把图片翻转正确
        if (facing == Facing.FRONT) {
            originalBitmap = PhotoBitmapUtils.convertBmp(originalBitmap, true);
        }
        return originalBitmap;
    }

    /**
     * 旋转当前屏幕上的文字 - 使用WatermarkHelper简化
     *
     * @param orientation
     */
    private void rotateText(int orientation) {
        // 记录当前方向
        currentOrientation = orientation;

        ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(ConstraintLayout.LayoutParams.WRAP_CONTENT, ConstraintLayout.LayoutParams.WRAP_CONTENT);

        switch (orientation) {
            case ORIGIN://下
                if (TextUtils.isEmpty(task_uuid)) {
                    mDatabind.llChangeProject.setVisibility(View.VISIBLE);
                } else {
                    mDatabind.ivBack.setVisibility(View.VISIBLE);
                }
                mDatabind.llChangeProject.setRotation(0);
                mDatabind.ivBack.setRotation(0);
                mDatabind.viewWaterMask.setRotation(0);
                layoutParams.startToStart = ConstraintSet.PARENT_ID;
                layoutParams.bottomToTop = mDatabind.llBottom.getId();
                layoutParams.goneBottomMargin = SizeUtils.dp2px(90);
                mDatabind.viewWaterMask.setLayoutParams(layoutParams);
                break;
            case RIGHT://右
                mDatabind.viewWaterMask.setPivotX(mDatabind.viewWaterMask.getWidth() - SizeUtils.dp2px(50));
                mDatabind.viewWaterMask.setPivotY(-SizeUtils.dp2px(80));
                layoutParams.bottomToTop = mDatabind.llBottom.getId();
                mDatabind.viewWaterMask.setRotation(-90);
                mDatabind.viewWaterMask.setLayoutParams(layoutParams);
                break;
            case LEFT://左
                mDatabind.viewWaterMask.setPivotX(0);
                mDatabind.viewWaterMask.setPivotY(0);
                layoutParams.width = mDatabind.cameraView.getHeight();
                layoutParams.startToStart = ConstraintSet.PARENT_ID;
                layoutParams.leftMargin = mDatabind.viewWaterMask.getHeight();
                layoutParams.topMargin = 0;
                mDatabind.viewWaterMask.setRotation(90);
                mDatabind.viewWaterMask.setLayoutParams(layoutParams);
                mDatabind.llChangeProject.setVisibility(View.GONE);
                mDatabind.ivBack.setVisibility(View.GONE);
                break;
            //暂时砍掉  不需要做这个
            case TOP://上
                mDatabind.viewWaterMask.setRotation(180);
                layoutParams.bottomToTop = mDatabind.llBottom.getId();
                layoutParams.endToEnd = ConstraintSet.PARENT_ID;
                layoutParams.rightMargin = SizeUtils.dp2px(15);
                layoutParams.bottomMargin = SizeUtils.dp2px(15);
                layoutParams.topMargin = mDatabind.viewWaterMask.getWidth() / 3 + 15;
                mDatabind.viewWaterMask.setLayoutParams(layoutParams);
                break;
        }

        // 屏幕方向改变时，同时更新Logo位置
        updateLogoPositionForOrientation();
    }


    private void switchVideoMode(boolean b) {
        mDatabind.ivSetting.setVisibility(b ? View.VISIBLE : View.GONE);
        mDatabind.ivChangeCamera.setVisibility(b ? View.VISIBLE : View.GONE);
        mDatabind.ivFlash.setVisibility(b ? View.VISIBLE : View.GONE);
    }


    private void toggleCamera(boolean isOpen) {
        if (captureButton != null) {
            captureButton.setEnabled(isOpen);
        }
    }

    public String formatTime(long time) {
        long minutes = TimeUnit.SECONDS.toMinutes(time) % 60;
        if (time >= 60 * 60) {
            minutes = 60;
        }
        long seconds = time % 60;
        return String.format("%d:%02d", minutes, seconds);

    }

    /**
     * 计算值
     *
     * @param source
     * @return
     */
    private Size findSize(List<Size> source) {
        LogXmManager.log("相机 findSize");

        double targetRatio = (double) 3 / 4;//预览标准比值
//        double targetRatio = (double) 9 / 16;//预览标准比值
        double aspectTolerance = 0.1;//预览标准比值
        double minDiff = Double.MAX_VALUE;
        //⚠️注意⚠️注意⚠️注意⚠️注意 改这里的比列，一定要去改合成水印哪里的代码
        int targetHeight = 1440;
        int targetWidth = 1080;
//        int targetHeight = 1920; // 对应16:9宽高比
//        int targetWidth = 1080; // 对应16:9宽高比
        Size optimalSize = null;

        // Try to find an size match aspect ratio and size
        for (Size size : source) {
            double ratio = size.getWidth() / (float) size.getHeight();
            if (abs(ratio - targetRatio) > aspectTolerance) {
                continue;
            }
            int diff = abs(size.getWidth() - targetWidth) + abs(size.getHeight() - targetHeight);
            if (diff < minDiff) {
                optimalSize = size;
                minDiff = diff;
            }
        }
        // Cannot find the one match the aspect ratio, ignore the requirement
        if (optimalSize == null) {
            minDiff = Double.MAX_VALUE;
            for (Size size : source) {
                int diff = abs(size.getHeight() - targetHeight) + abs(size.getWidth() - targetWidth);
                if (diff < minDiff) {
                    optimalSize = size;
                    minDiff = diff;
                }
            }
        }
        LogUtils.e("实际计算尺寸" + optimalSize);
        LogXmManager.log("实际计算尺寸 " + optimalSize);

        return optimalSize;
    }

    /**
     * 关闭当前界面，跳转到首页的内容
     */
    private void closeCamera(View mView, int position) {
        mView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mVideoTime != null) {
                    mVideoTime.dispose();
                    mVideoTime = null;
                }
                if (disposableLocation != null) {
                    disposableLocation.dispose();
                    disposableLocation = null;
                }
                stopLocationDisposable();
                stopBaiduLocation();
                stopUpdateBaiduTime();
                stopUpdateTime();
                AppExtKt.finishActivity(WatermarkCameraActivity.class);
                overridePendingTransition(0, 0);
                App.getAppViewModelInstance().getBackPosition().setValue(position);
            }
        });

    }


    @Override
    public void onRequestSuccess() {

        ///请求配置方案的详情接口
        mViewModel.getCameraConfigData().observe(this, new Observer<CameraConfigData>() {
            @Override
            public void onChanged(CameraConfigData config) {
                CommonUtils.updateUserOneConfig(config);
                try{
                    //设置当前的位置
                    if (!TextUtils.isEmpty(config.getWatermark_logo_pos())) {
                        logoPosition = Integer.parseInt(config.getWatermark_logo_pos());
                    }
                    LogUtils.e("相机配置相关--" + config.toString());
                    //获取自定义 logo 设置的倍数
                    if (!TextUtils.isEmpty(config.getWatermark_logo_scale())) {
                        logoScale = Integer.parseInt(config.getWatermark_logo_scale());
                        int logoWidth = Integer.parseInt(config.getWatermark_logo_width());
                        int logoHeight = Integer.parseInt(config.getWatermark_logo_height());
                        //根据倍数去对ivCustomLogo 进行缩放展示
                        updateReViewCustomLogo(logoScale, logoWidth, logoHeight);
                    }
                    //设置自定义的图片
                    GlideUtil.loadPicNoPlaceholder(WatermarkCameraActivity.this, config.getWatermark_logo(), mDatabind.ivCustomLogo);
                    //更新布局中ivCustomLogo的位置，每点击一次，就更新一下ivCustomLogo位置  一共有4个位置 1.默认是在viewWaterMask上面 2.左上角 3.右上角 4.屏幕中央显示 注意ivCustomLogo的位置在最上层
                    updateLogoPosition();
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        });

        ///每次进来都来请求动态替换Key
        mViewModel.getMapInfo().observe(this, new Observer<MapInfo>() {
            @Override
            public void onChanged(MapInfo mapInfo) {
                MMKVHelper.putString(ConstantMMVK.A_MAP_KEY, mapInfo.getMap_value());
            }
        });

        ///逆地理编码
        mViewModel.getRegeo().observe(this, new Observer<RegeoEntity>() {
            @Override
            public void onChanged(RegeoEntity regeoEntity) {
                isGetBaiduLoc = true;
                currentAddress = regeoEntity.getStreet();
                if (!TextUtils.isEmpty(currentAddress)) {
                    updateWaterMaskAddress(currentAddress + "(GPS)");
                    //原生定位 获取到的是哈
                    LogXmManager.log("通过gps定位 逆地理编码成功，来对比地址了");
                    compareLocationIfNearby();
                } else {
                    updateWaterMaskAddress("网络不畅，导致定位异常，请退出App重试");
                }
            }
        });

        mViewModel.getUserOne().observe(this, new Observer<UserInfo>() {
            @Override
            public void onChanged(UserInfo userInfo) {
                ToastUtil.show("操作成功");
                //设置图片的配置 是否标记 1 是 2 否
                if ("1".equals(userInfo.getUser().getIs_mark())) {
                    Constant.CAMERA_MARK_PHOTO = true;
                } else {
                    Constant.CAMERA_MARK_PHOTO = false;
                }
                //设置图片的配置 是否保存水印图片 1 是 2 否
                if ("1".equals(userInfo.getUser().getIs_save_to_phone())) {
                    Constant.CAMERA_SAVE_PHOTO_VIDEO = true;
                } else {
                    Constant.CAMERA_SAVE_PHOTO_VIDEO = false;
                }
                //设置图片的配置 是否保存原始图片 1 是 2 否
                if ("1".equals(userInfo.getUser().getIs_save_origin_pic())) {
                    Constant.CAMERA_SAVE_ORIGINAL_PHOTO = true;
                } else {
                    Constant.CAMERA_SAVE_ORIGINAL_PHOTO = false;
                }
                //设置项目
                if (userInfo.getProject() != null) {
                    mDatabind.tvProject.setText(userInfo.getProject().getProject_short_name());
                    project_uuid = userInfo.getProject().getUuid();
                }
            }
        });

        mViewModel.getSettingState().observe(this, new Observer<Object>() {
            @Override
            public void onChanged(Object o) {
                mViewModel.requestUserInfoData();
            }
        });

        //拿到地址信息，做对比
        mViewModel.getAddressManager().observe(this, new Observer<AddressEntity>() {
            @Override
            public void onChanged(AddressEntity addressEntity) {
                MMKVHelper.putString(ConstantMMVK.COMPANY_ALL_CLOCK_ADDRESS, JSON.toJSONString(addressEntity));//保存地址到本地
            }
        });

        //拿到项目详情的打卡范围
        mViewModel.getProjectOneEntity().observe(this, new Observer<ClockRadiusEntity>() {
            @Override
            public void onChanged(ClockRadiusEntity projectOneEntity) {
//                MMKVHelper.putString(ConstantMMVK.USER_CLOCK_RADIUS, JSON.toJSONString(projectOneEntity));//保存地址到本地
            }
        });

        //更新时间
        mViewModel.getTimestampEntity().observe(this, new Observer<TimestampEntity>() {
            @Override
            public void onChanged(TimestampEntity timestampEntity) {
                if (timestampEntity != null && !TextUtils.isEmpty(timestampEntity.getTimestamp())) {
                    MMKVHelper.putString(ConstantMMVK.TIME, "" + (Long.parseLong(timestampEntity.getTimestamp()) * 1000));
                    MMKVHelper.putString(ConstantMMVK.TIME_INIT_BOOT, "" + TimeClockUtil.getCurrentSystemUptimeInMillis());
                    createTimer();
                }
            }
        });

        //获取企业配置
        mViewModel.getWorkRulesEntity().observe(this, new Observer<WorkRulesEntity>() {
            @Override
            public void onChanged(WorkRulesEntity workRulesEntity) {
                CommonUtils.updateCompanyConfig(workRulesEntity);
                //判断当前项目是不是集体打卡的项目 ##注意这里就是单独领出来写的哈
                if (!TextUtils.isEmpty(project_uuid)) {
                    String nowProjectUUID = project_uuid;
                    boolean isMatched = false; // 添加一个标志来记录是否匹配

                    for (int i = 0; i < workRulesEntity.getProject_list().size(); i++) {
                        if (nowProjectUUID.equals(workRulesEntity.getProject_list().get(i).getUuid())) {
                            //匹配UUID，找到uuid 说明当前项目是可以集体打卡的项目
                            isMatched = true;
                            Constant.IS_COLLECTIVE_CLOCK_IN = true;
                            break;
                        }
                    }
                    // 如果没有匹配成功，设置为false
                    if (!isMatched) {
                        Constant.IS_COLLECTIVE_CLOCK_IN = false;
                    }
                } else {
                    Constant.IS_COLLECTIVE_CLOCK_IN = false;
                }

                //如果等于false 说明没集体大卡
                if (Constant.IS_COLLECTIVE_CLOCK_IN && !Constant.ROLE_CLEANER) {
                    mAdapter.remove("集体打卡");
                    mAdapter.addData("集体打卡");
                    tagUpdate(0);
                } else {
                    mAdapter.remove("集体打卡");
                    tagUpdate(0);
                }
                mAdapter.notifyDataSetChanged();
            }
        });

    }

    /**
     * 更新自定义水印 logo 缩放的比例
     * 根据Flutter的计算逻辑进行动态缩放
     */
    private void updateReViewCustomLogo(int scalePhoto, int originalWidth, int originalHeight) {
        // 计算实际的缩放比例，使用Flutter的算法
        double actualScale = calculateImageScale(scalePhoto, originalWidth, originalHeight, 0.05);

        // 获取当前的 LayoutParams
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mDatabind.ivCustomLogo.getLayoutParams();

        // 设置新的宽高
        params.width = (int) (originalWidth * actualScale);  // 计算新的宽度
        params.height = (int) (originalHeight * actualScale); // 计算新的高度

        // 应用新的 LayoutParams
        mDatabind.ivCustomLogo.setLayoutParams(params);

        LogUtils.e("Logo缩放 - 原始尺寸: " + originalWidth + "x" + originalHeight +
                ", 用户缩放: " + scalePhoto + "%, 实际缩放: " + actualScale +
                ", 最终尺寸: " + params.width + "x" + params.height);
    }

    /**
     * 根据图片尺寸动态计算缩放比例
     * 移植自Flutter的计算逻辑
     * 目标：滑动到100%时就是合适的显示大小，不能再大了
     * 小图片最大缩放不超过0.5倍，大图片基础缩放0.12倍最大0.4倍
     *
     * @param scalePhoto  用户设置的缩放比例 (30-100)
     * @param imageWidth  图片原始宽度
     * @param imageHeight 图片原始高度
     * @param minScale    最小缩放比例
     * @return 实际应用的缩放比例
     */
    private double calculateImageScale(int scalePhoto, int imageWidth, int imageHeight, double minScale) {
        // 将用户缩放比例转换为0.3-1.0范围
        double userScale = scalePhoto / 100.0; // 用户设置的缩放比例 (0.3-1.0)

        // 如果图片尺寸未获取到，使用默认缩放
        if (imageWidth == 0 || imageHeight == 0) {
            return userScale * 0.3;
        }

        // 计算图片的最大尺寸
        int maxDimension = Math.max(imageWidth, imageHeight);

        double maxUserScale; // 100%时的最大缩放比例

        if (maxDimension <= 150) {
            // 小图片：100%时最大0.5倍
            maxUserScale = 0.5;
        } else if (maxDimension >= 1000) {
            // 大图片（如1080*1440）：100%时最大0.12倍，防止占满屏幕
            maxUserScale = 0.12;
        } else {
            // 中等图片：根据尺寸线性插值计算最大缩放
            // 150-1000之间，最大缩放从0.5线性减少到0.12
            double ratio = (maxDimension - 150.0) / (1000.0 - 150.0);
            maxUserScale = 0.5 - (ratio * 0.38); // 从0.5减少到0.12 (0.5-0.12=0.38)
        }

        // 用户缩放比例映射到实际缩放范围
        // userScale范围是0.3-1.0，映射到minScale-maxUserScale
        double actualScale = minScale + (userScale - 0.3) / 0.7 * (maxUserScale - minScale);

        // 确保不超过最小和最大限制
        actualScale = Math.max(minScale, Math.min(maxUserScale, actualScale));

        LogUtils.e("图片缩放计算 - 最大尺寸: " + maxDimension +
                ", 最大用户缩放: " + maxUserScale +
                ", 用户缩放: " + userScale +
                ", 实际缩放: " + actualScale);

        return actualScale;
    }

    private void tagUpdate(int position) {
        changeFacingStyle(mAdapter.getData().get(position));
        mAdapter.updateSingleItem(mAdapter.getData().get(position));
        mDatabind.recycler.smoothScrollToPosition(position);
    }

    // WGS-84 转换为 GCJ-02 的方法
    private LatLng wgs84ToGcj02(LatLng sourceLatLng) {
        CoordinateConverter converter = new CoordinateConverter(WatermarkCameraActivity.this);
        // CoordType.GPS 待转换坐标类型
        converter.from(CoordinateConverter.CoordType.GPS);
        // sourceLatLng待转换坐标点 LatLng类型
        converter.coord(sourceLatLng);
        // 执行转换操作
        return converter.convert();
    }


    /**
     * 原生的定位回调
     */
    private class MyListener implements GPSLocationListener {

        @Override
        public void UpdateLocation(Location location) {
            LogUtils.e(" 来进行原生定位了" + location);
            LogXmManager.log("来进行原生定位了 暂停高德定位了");
            if (mLocationService != null) {
                mLocationService.stop();
            }
            gpsLocationManager.stop();
            if (location != null) { //gps 定位优化
                LogXmManager.log("来进行原生定位了 location 有值，获取到经纬度了，通过接口来拿数据");
                // 获取原始经纬度
                double wgs84Latitude = location.getLatitude();
                double wgs84Longitude = location.getLongitude();

                // 将 WGS-84 转换为 GCJ-02
                LatLng gcj02LatLng = wgs84ToGcj02(new LatLng(wgs84Latitude, wgs84Longitude));

                currentLatitude = gcj02LatLng.latitude;
                currentLongitude = gcj02LatLng.longitude;
                LogUtils.e("来进行原生Gps定位了 定位成功 -- " + currentLatitude + " ; " + currentLongitude);

                // 通过经纬度请求高德获取地址
                mViewModel.getRegeo(currentLatitude, currentLongitude);
            } else {
                LogXmManager.log("来进行原生Gps定位了 但是 location 为空");
                LogUtils.e(" 来进行原生Gps定位了 但是 location 为空-- " + currentLatitude + " ; " + currentLongitude);
                if (!isLocationOpen(WatermarkCameraActivity.this)) {
                    updateWaterMaskAddress("您手机未开启定位功能");
                } else {
                    updateWaterMaskAddress("网络不畅，导致定位异常");
                    LogXmManager.log("gps 也没获取到，重新请求网络定位");
                    getCurrentLocation();//重新请求网络定位
                }
            }
        }

        @Override
        public void UpdateStatus(String provider, int status, Bundle extras) {
            Log.e("定位的类型", "UpdateStatus -- provider - " + provider + " ; status = " + status);
        }

        @Override
        public void UpdateGPSProviderStatus(int gpsStatus) {
            Log.e("定位的类型", "UpdateStatus -- gpsStatus - " + gpsStatus);
            switch (gpsStatus) {
                case GPSProviderStatus.GPS_ENABLED:
                    ToastUtil.show("GPS开启");
                    break;
                case GPSProviderStatus.GPS_DISABLED:
                    ToastUtil.show("GPS关闭");
                    break;
                case GPSProviderStatus.GPS_OUT_OF_SERVICE:
                    ToastUtil.show("GPS不可用");
                    break;
                case GPSProviderStatus.GPS_TEMPORARILY_UNAVAILABLE:
                    ToastUtil.show("GPS暂时不可用");
                    break;
                case GPSProviderStatus.GPS_AVAILABLE:
                    ToastUtil.show("GPS可用啦");
                    break;
            }
        }

    }


    /**
     * 高德的定位回调
     */
    private AMapLocationListener aMapLocationListener = new AMapLocationListener() {

        @Override
        public void onLocationChanged(AMapLocation aMapLocation) {
            if (null != aMapLocation) {
                gMapLocation = aMapLocation;
                if (mLocationService != null) {
                    mLocationService.stop();
                }
                LogXmManager.log("高德定位成功了 - " + aMapLocation.getErrorCode());
                LogXmManager.log("高德定位成功了 - " + aMapLocation.getErrorInfo());
                LogXmManager.log("高德定位成功了 - " + aMapLocation.getLocationDetail());

                if (!isLocationOpen(WatermarkCameraActivity.this)) {
                    updateWaterMaskAddress("提醒您，请开启手机的定位服务。");
                    return;
                }
                if (aMapLocation.getErrorCode() == 0) {
                    isGetBaiduLoc = true;
                    LogUtils.e("onLocationChanged 高德定位成功了，loctype -- > " + aMapLocation.getLocationDetail());
                    LogUtils.e("onLocationChanged 高德定位成功了，经纬度是 -- >" + aMapLocation.getLatitude() + "," + aMapLocation.getLongitude());

                    currentLatitude = aMapLocation.getLatitude();
                    currentLongitude = aMapLocation.getLongitude();
                    ///当前当前显示的位置
                    if (!TextUtils.isEmpty(aMapLocation.getAddress())) {
                        currentAddress = aMapLocation.getAddress();
                    } else if (!TextUtils.isEmpty(aMapLocation.getPoiName())) {
                        currentAddress = aMapLocation.getPoiName();
                    }
                    //重新设置下地址
                    updateWaterMaskAddress(currentAddress);
                    //对比本地文件下适合的地址
                    compareLocationIfNearby();
                } else {
                    updateWaterMaskAddress("定位失败，错误码：" + aMapLocation.getErrorCode());
                }
            } else {
                LogXmManager.log("高德定位失败了 aMapLocation空的- ");
                updateWaterMaskAddress("网络不畅，导致定位异常");
            }
        }
    };

    /**
     * 设置水印组件的地址信息
     */
    private void updateWaterMaskAddress(String currentAddress) {
        LogXmManager.log(currentAddress);
        if (NetUrl.defaultUrl.equals(NetUrl.SERVER_URL_RELEASE)) {
            mDatabind.viewWaterMask.getTvAddress().setText(currentAddress);
            mDatabind.viewWaterMask1.getTvAddress().setText(currentAddress);
        } else {
            mDatabind.viewWaterMask.getTvAddress().setText(currentAddress + ";" + currentLatitude + "," + currentLongitude);
            mDatabind.viewWaterMask1.getTvAddress().setText(currentAddress + ";" + currentLatitude + "," + currentLongitude);
        }
    }

    /**
     * 设置水印组件的时间信息
     */
    private void updateWaterMaskDate(Date date) {
        mDatabind.viewWaterMask.updateTime(date);
        mDatabind.viewWaterMask1.updateTime(date);
    }


    /**
     * 45     * 比较当前经纬度与给定地址列表中的位置，如果找到距离小于300米的地址，则更新当前经纬度和地址。
     */
    public void compareLocationIfNearby() {
        LogExtKt.logE("来对比本地地址", TAG);
        String addressJson = MMKVHelper.getString(ConstantMMVK.COMPANY_ALL_CLOCK_ADDRESS);//保存地址到本地
        if (!TextUtils.isEmpty(addressJson)) {
            LogExtKt.logE("来对比本地地址 进来了", TAG);
            AddressEntity addressEntity = new Gson().fromJson(addressJson, AddressEntity.class);
            if (addressEntity != null && addressEntity.getList() != null && addressEntity.getList().size() > 0) {
                // 使用局部变量保存当前经纬度
                double compareLatitude = currentLatitude;
                double compareLongitude = currentLongitude;
                LogUtils.e("地址对比之前的经纬度->" + "当前经度纬度是:" + compareLatitude + " ; " + compareLongitude);
                //获取当前的范围
                String range = MMKVHelper.getString(ConstantMMVK.CLOCK_IN_RANGE, "300");
                LogUtils.e("从本地取的范围 - " + range);

                for (AddressListEntity list : addressEntity.getList()) {
                    try {
                        double targetLatitude = Double.parseDouble(list.getLat());
                        double targetLongitude = Double.parseDouble(list.getLnt());

                        // 计算两个坐标点之间的距离
                        double distance = AMapUtils.calculateLineDistance(new LatLng(compareLatitude, compareLongitude), new LatLng(targetLatitude, targetLongitude));

                        LogExtKt.logE("地址对比出来的距离->" + "当前地址是:" + list.getAddress() + " ; 计算出来的距离是 ->" + distance, "");

                        // 如果距离小于300米，更新当前经纬度和地址，并停止循环

                        if (distance < Integer.parseInt(range)) {
                            LogXmManager.log("当前距离小于 " + range + "米了 具体位置是" + list.getAddress());
                            LogUtils.e("当前距离小于" + range + "米了 具体位置是 - " + list.getAddress() + " ；要是不信，看看计算出来的值吧 " + distance);
                            // 更新当前经纬度和地址 不要地址的经纬度，要真实的经纬度
//                            currentLatitude = targetLatitude;
//                            currentLongitude = targetLongitude;
                            currentAddress = list.getAddress();
                            updateWaterMaskAddress(currentAddress);
                            break;
                        }
                    } catch (NumberFormatException e) {
                        LogXmManager.log("解析经纬度时出现错误 ");

                        // 处理解析失败的情况
                        LogUtils.e("解析经纬度时出现错误: " + e.getMessage());
                    }
                }

            }
        }

    }

    /**
     * 启动本地计时器
     */
    private void startUpdateTime() {
        //获取保存在本地的时间戳
        String localTime = MMKVHelper.getString(ConstantMMVK.TIME, "");
        if (!TextUtils.isEmpty(localTime)) {
            initialServerTime = Long.parseLong(localTime);
        }
        String initBootTime = MMKVHelper.getString(ConstantMMVK.TIME_INIT_BOOT, "");
        if (!TextUtils.isEmpty(initBootTime)) {
            initialBootTime = Long.parseLong(initBootTime);
        }

        //本地存储的启动时间 跟现在本机的启动时间 做对比，如果当前大于本机启动时间，说明重启过了
//        if (initialBootTime < TimeClockUtil.getCurrentSystemUptimeInMillis()) {
//
//        }

        if (initialServerTime != -1 && initialBootTime != -1) {
            updateRunnable = this::updateTime;
            updateTime(); // 立即更新一次，然后再设置延迟任务
            handlerLocTime.postDelayed(updateRunnable, 1000); // 每秒更新一次
        } else {
            mViewModel.requestTime();
            runOnUiThread(() -> {
                updateWaterMaskDate(null);
            });
        }
    }

    /**
     * 更新本地计时器
     */
    private void updateTime() {
        final long currentTime = TimeClockUtil.getCurrentAccurateTime(initialServerTime, initialBootTime);

        // 创建 Date 对象
        nowLocalDate = new Date(currentTime);

        // 使用 SimpleDateFormat 格式化输出
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        String formattedDate = dateFormat.format(nowLocalDate);

//        LogUtils.e("" + formattedDate);

        runOnUiThread(() -> {
            updateWaterMaskDate(nowLocalDate);
        });

        // 如果仍然需要继续更新，则重新安排下一次更新
        handlerLocTime.postDelayed(updateRunnable, 1000); // 每秒更新一次
    }

    /**
     * 停止本地计时器
     */
    private void stopUpdateTime() {
        if (updateRunnable != null) {
            handlerLocTime.removeCallbacks(updateRunnable);
        }
    }

    private void stopUploadMonitor() {
        if (handlderMonitorRunnable != null) {
            handlerUpload.removeCallbacks(handlderMonitorRunnable);
        }
    }

    private void stopUpdateBaiduTime() {
        if (baiduLocRunnable != null) {
            handlerBaiduLocTime.removeCallbacks(baiduLocRunnable);
        }
    }


    @Override
    protected void onRestart() {
        super.onRestart();
        LogUtils.e("onRestart 重启了");
        //把每次的从系统获取的时间存储进去
        String backendTime = MMKVHelper.getString(ConstantMMVK.APP_BACKEND_TIME, "");
        if (!TextUtils.isEmpty(backendTime)) {
//            if (System.currentTimeMillis() - Long.parseLong(backendTime) >= (60 * 60 * 1000)) {//后台时间超过 指定的时间，重新启动App 3600000秒，是一个小时
            if (System.currentTimeMillis() - Long.parseLong(backendTime) >= (5 * 60 * 1000)) {//后台时间超过 指定的时间，重新启动App 3600000秒，是一个小时
                LogUtils.e("时间比对 , 重启App");
                CommonUtils.restartApp(WatermarkCameraActivity.this);
            } else {
                LogUtils.e("时间比对 , if没过 系统的 -- " + mVideoTime + " ; 后台的 " + backendTime);
            }
        } else {
            LogUtils.e("时间比对 , 本地没值");
        }
        getCurrentLocation();
        createTimer();
    }


    @Override
    protected void onResume() {
        super.onResume();
    }


    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onPause() {
        super.onPause();
        mDatabind.ivFlash.setVisibility(View.VISIBLE);
        mDatabind.ivChangeCamera.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(task_uuid)) {
            mDatabind.ivSetting.setVisibility(View.VISIBLE);
        }
        showFocuseView = false;
        mDatabind.cfvView.setVisibility(View.GONE);
        if (mDatabind.cameraView.isTakingVideo()) {
            mDatabind.cameraView.stopVideo();
        }
        stopUpdateBaiduTime();
        stopLocationDisposable();
        stopUpdateTime();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        finishActivityAll();
    }

    private void stopBaiduLocation() {
        if (mLocationService != null) {
            if (aMapLocationListener != null) {
                mLocationService.unregisterListener();
                mLocationService.stop();
                mLocationService.destroy();
            }
            mLocationService = null;
            aMapLocationListener = null;
        }
    }

    /**
     * 关闭当前界面下可销毁的对象
     */
    private void finishActivityAll() {
        // 注销广播接收器
        unregisterLocationModeChangeReceiver();
        stopLocationDisposable();
        stopVideoDisposable();
        stopUpdateBaiduTime();
        stopBaiduLocation();
        stopUpdateTime();
        stopUploadMonitor();
    }

    private void unregisterLocationModeChangeReceiver() {
        if (gpsLocationManager != null) {
            gpsLocationManager.stop();
        }
        if (locationModeChangeReceiver != null) {
            try {
                unregisterReceiver(locationModeChangeReceiver);
            } catch (IllegalArgumentException e) {
                LogUtils.e("Receiver not registered: " + locationModeChangeReceiver.toString());
            }
            locationModeChangeReceiver = null;
        }
    }

    private void stopLocationDisposable() {
        if (disposableLocation != null && !disposableLocation.isDisposed()) {
            disposableLocation.dispose();
            disposableLocation = null;
        }
    }

    private void stopVideoDisposable() {
        if (mVideoTime != null && !mVideoTime.isDisposed()) {
            mVideoTime.dispose();
            mVideoTime = null;
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && TextUtils.isEmpty(task_uuid)) {
            if (backPressedTime + TIME_INTERVAL > System.currentTimeMillis()) {
                AppExtKt.finishAllActivity();
                System.exit(0);
            } else {
                Toast.makeText(this, "再次点击返回按钮退出应用", Toast.LENGTH_SHORT).show();
            }

            backPressedTime = System.currentTimeMillis();
            return false;
        } else {
            return super.onKeyDown(keyCode, event);
        }
    }

    ///震动
    private void vibratePhone() {
        // 获取 Vibrator 服务
        Vibrator vibrator = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
        if (vibrator != null) {
            // 震动 500 毫秒
            vibrator.vibrate(500);
        }
    }

    @Override
    public void onNetworkStateChanged(@NonNull NetState netState) {
        super.onNetworkStateChanged(netState);
        if (netState.isSuccess()) {
            currentAddress = "网络变化，重新获取定位地址中...";
            updateWaterMaskAddress(currentAddress);
            getCurrentLocation();
            if (initialServerTime == -1 && initialBootTime == -1) {
                mViewModel.requestTime();
            }
        }
    }

    @Override
    public void onLocationStatusChanged(boolean isEnabled) {
        //监听用户定位服务是否开启
        if (isEnabled) {
            currentAddress = "权限变化，重新获取定位地址中...";
            updateWaterMaskAddress(currentAddress);
            getCurrentLocation();
        }
        ToastUtil.show(isEnabled ? "定位服务已开启" : "定位服务未开启，请确保已开启定位服务");
    }

    /**
     * 更新自定义Logo的位置（点击设置按钮时调用）
     * 循环切换4个位置：默认是 2， 1左上 2左下 3居中 4右上
     */
    private void updateLogoPosition() {
        // 更新Logo位置
        updateLogoPositionForOrientation();
    }

    /**
     * 根据当前屏幕方向和Logo位置状态更新Logo位置
     */
    private void updateLogoPositionForOrientation() {
        // 获取ConstraintLayout.LayoutParams
        ConstraintLayout.LayoutParams params = (ConstraintLayout.LayoutParams) mDatabind.ivCustomLogo.getLayoutParams();

        // 清除所有约束
        params.startToStart = ConstraintLayout.LayoutParams.UNSET;
        params.startToEnd = ConstraintLayout.LayoutParams.UNSET;
        params.endToStart = ConstraintLayout.LayoutParams.UNSET;
        params.endToEnd = ConstraintLayout.LayoutParams.UNSET;
        params.topToTop = ConstraintLayout.LayoutParams.UNSET;
        params.topToBottom = ConstraintLayout.LayoutParams.UNSET;
        params.bottomToTop = ConstraintLayout.LayoutParams.UNSET;
        params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET;

        // 重置所有margin和旋转
        params.setMargins(0, 0, 0, 0);
        params.goneBottomMargin = 0;
        params.leftMargin = 0;
        params.rightMargin = 0;
        params.topMargin = 0;
        params.bottomMargin = 0;
        mDatabind.ivCustomLogo.setRotation(0);

        // 根据当前方向和Logo位置设置约束
        switch (currentOrientation) {
            case ORIGIN: // 正常方向
                updateLogoForOriginOrientation(params);
                break;
            case LEFT: // 左旋转270度
                updateLogoForLeftOrientation(params);
                break;
            case RIGHT: // 右旋转90度（虽然代码中注释说暂时不用，但保留逻辑）
                updateLogoForRightOrientation(params);
                break;
            case TOP: // 180度旋转
                updateLogoForTopOrientation(params);
                break;
        }

        // 应用新的布局参数
        mDatabind.ivCustomLogo.setLayoutParams(params);
    }

    /**
     * 正常方向下的Logo位置设置
     */
    private void updateLogoForOriginOrientation(ConstraintLayout.LayoutParams params) {
        switch (logoPosition) {
            case 2: // 默认位置：在viewWaterMask上面
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.view_water_mask;
                params.leftMargin = SizeUtils.dp2px(10);
                params.bottomMargin = SizeUtils.dp2px(10);
                break;

            case 1: // 左上角
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.leftMargin = SizeUtils.dp2px(20);
                params.topMargin = SizeUtils.dp2px(80);
                break;

            case 4: // 右上角
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.rightMargin = SizeUtils.dp2px(20);
                params.topMargin = SizeUtils.dp2px(80);
                break;

            case 3: // 屏幕中央
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                break;
        }
    }

    /**
     * 左旋转方向下的Logo位置设置
     */
    private void updateLogoForLeftOrientation(ConstraintLayout.LayoutParams params) {
        // Logo需要旋转90度以保持正确显示
        mDatabind.ivCustomLogo.setRotation(90);

        switch (logoPosition) {
            case 2: // 默认位置：跟随水印位置
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.leftMargin = mDatabind.viewWaterMask.getHeight() + SizeUtils.dp2px(10);
                params.topMargin = SizeUtils.dp2px(10);
                break;

            case 1: // 左上角（在左旋转时变成右上角）
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.rightMargin = SizeUtils.dp2px(20);
                params.topMargin = SizeUtils.dp2px(80);
                break;

            case 4: // 右上角（在左旋转时变成右下角）
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.ll_bottom;
                params.rightMargin = SizeUtils.dp2px(20);
                params.bottomMargin = SizeUtils.dp2px(80);
                break;

            case 3: // 屏幕中央
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                break;
        }
    }

    /**
     * 右旋转方向下的Logo位置设置
     */
    private void updateLogoForRightOrientation(ConstraintLayout.LayoutParams params) {
        // Logo需要旋转-90度以保持正确显示
        mDatabind.ivCustomLogo.setRotation(-90);

        switch (logoPosition) {
            case 2: // 默认位置：跟随水印位置，在水印上方
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.view_water_mask;
                params.rightMargin = SizeUtils.dp2px(30);
                params.bottomMargin = SizeUtils.dp2px(10);
                break;

            case 1: // 左上角（在右旋转时变成左下角）
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.ll_bottom;
                params.leftMargin = SizeUtils.dp2px(20);
                params.bottomMargin = SizeUtils.dp2px(80);
                break;

            case 4: // 右上角（在右旋转时变成左上角）
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.leftMargin = SizeUtils.dp2px(20);
                params.topMargin = SizeUtils.dp2px(80);
                break;

            case 3: // 屏幕中央
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                break;
        }
    }

    /**
     * 180度旋转方向下的Logo位置设置
     */
    private void updateLogoForTopOrientation(ConstraintLayout.LayoutParams params) {
        // Logo需要旋转180度以保持正确显示
        mDatabind.ivCustomLogo.setRotation(180);

        switch (logoPosition) {
            case 2: // 默认位置：在水印上方（180度时在下方）
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToBottom = R.id.view_water_mask;
                params.rightMargin = SizeUtils.dp2px(10);
                params.topMargin = SizeUtils.dp2px(10);
                break;

            case 1: // 左上角（在180度时变成右下角）
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.ll_bottom;
                params.rightMargin = SizeUtils.dp2px(20);
                params.bottomMargin = SizeUtils.dp2px(80);
                break;

            case 4: // 右上角（在180度时变成左下角）
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToTop = R.id.ll_bottom;
                params.leftMargin = SizeUtils.dp2px(20);
                params.bottomMargin = SizeUtils.dp2px(80);
                break;

            case 3: // 屏幕中央
                params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID;
                params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID;
                params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID;
                params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID;
                break;
        }
    }
}