package com.business_clean.ui.fragment.main;


import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.GridLayoutManager;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.business_clean.R;
import com.business_clean.app.App;
import com.business_clean.app.base.BaseFragment;
import com.business_clean.app.config.ConstantMMVK;
import com.business_clean.app.ext.CommonUtils;
import com.business_clean.app.util.MMKVHelper;
import com.business_clean.app.util.ToastUtil;
import com.business_clean.data.mode.todo.TodoTotalEntity;
import com.business_clean.databinding.FragmentTodoBinding;
import com.business_clean.ui.adapter.BaseStringAdapter;
import com.business_clean.ui.adapter.viewpager2.FragmentLazyStateAdapter;
import com.business_clean.ui.fragment.todo.TodoItemFragment;
import com.business_clean.ui.fragment.todo.TodoPlanFragment;
import com.business_clean.ui.fragment.todo.TodoPlanWorkFragment;
import com.business_clean.viewmodel.request.TodoViewModel;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.gyf.immersionbar.ImmersionBar;
import com.luck.picture.lib.decoration.MyGridSpacingItemDecoration;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;

public class TodoFragment extends BaseFragment<TodoViewModel, FragmentTodoBinding> {

    private boolean isLazyLoadTag = false; //是否已经加载过tags

    private BaseStringAdapter mAdapter;

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        ImmersionBar.with(this).titleBar(getMDatabind().recyclerview).navigationBarColor(R.color.white).statusBarDarkFont(true).init();

        ArrayList<Fragment> mFragments = new ArrayList<>();

        TodoPlanFragment fragment1 = new TodoPlanFragment(1);
        TodoPlanFragment fragment2 = new TodoPlanFragment(2);
        TodoPlanFragment fragment3 = new TodoPlanFragment(3);
        TodoPlanWorkFragment fragment4 = new TodoPlanWorkFragment();
        TodoItemFragment fragment5 = new TodoItemFragment();


        mFragments.add(fragment1);
        mFragments.add(fragment2);
        mFragments.add(fragment3);
        mFragments.add(fragment4);
        mFragments.add(fragment5);

        List<String> stringList = new ArrayList<>();


        stringList.add("清洁");
        stringList.add("巡检");
        stringList.add("培训");
        stringList.add("工单");
        stringList.add("审批");

        getMDatabind().viewpagerTodo.setUserInputEnabled(false);
        getMDatabind().viewpagerTodo.setOffscreenPageLimit(mFragments.size());
        getMDatabind().viewpagerTodo.setAdapter(new FragmentLazyStateAdapter(getMActivity(), mFragments));
//        getMDatabind().viewpagerTodo.setOffscreenPageLimit(mFragments.size());
//        getMDatabind().tabLayout.setupWithViewPager(getMDatabind().viewpagerTodo);

        mAdapter = new BaseStringAdapter(1);
        mAdapter.setCustomSelectDrawable(CommonUtils.getBaseSelectedDrawable(getMActivity(), 4, R.color.base_primary_select, R.color.base_primary, 1));
        mAdapter.setSingleCancel(true);
        mAdapter.setBold(true);
        getMDatabind().recyclerview.setLayoutManager(new GridLayoutManager(getMActivity(), 5));
        getMDatabind().recyclerview.setAdapter(mAdapter);
        getMDatabind().recyclerview.addItemDecoration(new MyGridSpacingItemDecoration(5, SizeUtils.dp2px(6), true));

        getTagList(null);

        mAdapter.updateItem(0);

        //获取列表总数
        mViewModel.requestTodoTotal();
    }

    @Override
    public void onBindViewClick() {
        mAdapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@NonNull BaseQuickAdapter<?, ?> adapter, @NonNull View view, int position) {
                mAdapter.updateItem(position);
                getMDatabind().viewpagerTodo.setCurrentItem(position, false);
            }
        });

    }

    @Override
    public void initObserver() {
        //监听外面，底部点击的事哈请求切换更新数据
        App.getAppViewModelInstance().getRefreshTodoTotal().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity todoTotalEntity) {
//                LogUtils.e("哈哈哈哈哈哈 来这里了 ， " + todoTotalEntity.toString());
                getTagList(todoTotalEntity);
            }
        });


        App.getAppViewModelInstance().getTodoMeCreate().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mAdapter.updateItem(4);
                getMDatabind().viewpagerTodo.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            getMDatabind().viewpagerTodo.setCurrentItem(4, false);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });

        //说明创建员工了，那么跳转到审批
        App.getAppViewModelInstance().getRefreshMember().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                mAdapter.updateItem(4);
                getMDatabind().viewpagerTodo.post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            getMDatabind().viewpagerTodo.setCurrentItem(4, false);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        });
    }

    @Override
    public void lazyLoadData() {
    }

    @Override
    public void onResume() {
        super.onResume();
        ImmersionBar.with(this).titleBar(getMDatabind().recyclerview).navigationBarColor(R.color.white).statusBarDarkFont(true).init();

        boolean aBoolean = MMKVHelper.getBoolean(ConstantMMVK.JUMP_TODO_PAGE, false);
        if (aBoolean) {
            getMDatabind().viewpagerTodo.postDelayed(new Runnable() {
                @Override
                public void run() {
                    mAdapter.updateItem(4);
                    getMDatabind().viewpagerTodo.setCurrentItem(4, false);
                }
            }, 2000);
        }
    }

    @Override
    public void onRequestSuccess() {

        mViewModel.getTodoTotalEntity().observe(this, new Observer<TodoTotalEntity>() {
            @Override
            public void onChanged(TodoTotalEntity todoTotalEntity) {
                getTagList(todoTotalEntity);
            }
        });


    }


    private void getTagList(TodoTotalEntity data) {
        List<String> tags = new ArrayList<>();
        if (data != null) {
            tags.add(data.getCleaning_total() + "\n清洁");
            tags.add(data.getInspect_total() + "\n巡检");
            tags.add(data.getTraining_total() + "\n培训");
            tags.add(data.getWord_order_total() + "\n工单");
            tags.add(data.getSp_total() + "\n审批");


            //如果已经设置过一次了，就不再设置了，直接return
            if (isLazyLoadTag) {
                mAdapter.setList(tags);
                return;
            }

            if (!TextUtils.isEmpty(data.getCleaning_total()) && Integer.parseInt(data.getCleaning_total()) > 0) {
                mAdapter.updateItem(0);
                getMDatabind().viewpagerTodo.setCurrentItem(0, false);
            } else if (!TextUtils.isEmpty(data.getInspect_total()) && Integer.parseInt(data.getInspect_total()) > 0) {
                mAdapter.updateItem(1);
                getMDatabind().viewpagerTodo.setCurrentItem(1, false);
            } else if (!TextUtils.isEmpty(data.getTraining_total()) && Integer.parseInt(data.getTraining_total()) > 0) {
                mAdapter.updateItem(2);
                getMDatabind().viewpagerTodo.setCurrentItem(2, false);
            } else if (!TextUtils.isEmpty(data.getWord_order_total()) && Integer.parseInt(data.getWord_order_total()) > 0) {
                mAdapter.updateItem(3);
                getMDatabind().viewpagerTodo.setCurrentItem(3, false);
            } else if (!TextUtils.isEmpty(data.getSp_total()) && Integer.parseInt(data.getSp_total()) > 0) {
                mAdapter.updateItem(4);
                getMDatabind().viewpagerTodo.setCurrentItem(4, false);
            }

            isLazyLoadTag = true;
        } else {
            tags.add("0\n清洁");
            tags.add("0\n巡检");
            tags.add("0\n培训");
            tags.add("0\n工单");
            tags.add("0\n审批");
        }
        mAdapter.setList(tags);
    }


}
