package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.blankj.utilcode.util.AppUtils
import com.business_clean.app.config.Constant
import com.business_clean.app.network.NetUrl
import com.business_clean.data.initconfig.MapInfo
import com.business_clean.data.initconfig.TimestampEntity
import com.business_clean.data.mode.appupdate.NewVersionInfo
import com.business_clean.data.mode.camera.CameraConfigData
import com.business_clean.data.mode.init.InitDataEntity
import com.business_clean.data.mode.login.UserInfo
import com.business_clean.data.mode.project.ProjectMangerList
import com.business_clean.data.mode.project.WorkRulesEntity
import com.business_clean.data.mode.todo.TodoList
import com.business_clean.data.mode.todo.TodoTotalEntity
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import me.hgj.mvvmhelper.net.LoadingType
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 在main中需要获取的内容
 */
class MainViewModel : BaseViewModel() {


    var initData = MutableLiveData<InitDataEntity>()

    var userInfo = MutableLiveData<UserInfo>()

    var todoTotal = MutableLiveData<TodoTotalEntity>()

    var timestampEntity = MutableLiveData<TimestampEntity>()


    var errorEntity = MutableLiveData<Any>()


    //获取用工规则
    var workRulesEntity = MutableLiveData<WorkRulesEntity>()

    ///是否需要更新
    var newVersionInfo = MutableLiveData<NewVersionInfo>()


    //获取企业总部项目
    var projectHeadOfficeMangerList = MutableLiveData<ProjectMangerList>()


    ///map的info
    var mapInfo = MutableLiveData<MapInfo>()


    //获取个人的方案配置
    var cameraConfigData = MutableLiveData<CameraConfigData>()

    //获取源数据
    fun requestInitData() {
        rxHttpRequest {
            onRequest = {
                initData.value = RxHttp.get(NetUrl.GET_INIT_DATA)
                    .toResponse<InitDataEntity>().await()
            }
        }
    }

    /**
     * 获取个人信息
     */
    fun requestUserInfo() {
        rxHttpRequest {
            onRequest = {
                userInfo.value = RxHttp.get(NetUrl.GET_USER_INFO)
                    .toResponse<UserInfo>().await()
            }
        }
    }


    /**
     * 获取列表
     */
    fun requestTodoTotal() {
        rxHttpRequest {
            onRequest = {
                todoTotal.value = RxHttp.get(NetUrl.GET_TODO_TOTAL)
                    .toResponse<TodoTotalEntity>().await()
            }
            loadingType = LoadingType.LOADING_NULL
        }

    }


    /**
     * 拉取服务器时间
     */
    fun requestTime() {
        rxHttpRequest {
            onRequest = {
                timestampEntity.value = RxHttp.get(NetUrl.GET_TIME_STAMP).toResponse<TimestampEntity>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }


    /**
     * 获取用工规则的配置
     */
    fun requestWorkRules() {
        rxHttpRequest {
            onRequest = {
                workRulesEntity.value = RxHttp.get(NetUrl.GET_WORK_POST_RULES)
                    .toResponse<WorkRulesEntity>().await()
            }
        }
    }


    /**
     * 检查是否有更新
     */
    fun requestCheckApp() {
        rxHttpRequest {
            onRequest = {
                newVersionInfo.value = RxHttp.get(NetUrl.CHECK_UPDATE_APP)
                    .add("version", "" + AppUtils.getAppVersionName())
                    .toResponse<NewVersionInfo>().await()
            }
        }
    }

    /**
     * 获取企业总部项目
     */
    fun requestHeadProject() {
        rxHttpRequest {
            onRequest = {
                projectHeadOfficeMangerList.value = RxHttp.get(NetUrl.GET_ONE_HEAD_OFFICE)
                    .toResponse<ProjectMangerList>().await()
            }
        }
    }


    /**
     * 动态拉取高德地图服务的Key
     */
    fun requestAMapInfo() {
        rxHttpRequest {
            onRequest = {
                mapInfo.value = RxHttp
                    .get(NetUrl.GET_A_MAP_KEY)
                    .add("map_code", "gao_de")
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<MapInfo>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }

    /**
     * 获取个人配置 CameraConfigData
     */
    fun requestUserWorkConfig() {
        rxHttpRequest {
            onRequest = {
                cameraConfigData.value = RxHttp.get(NetUrl.GET_USER_ONE_CAMERA)
                    .toResponse<CameraConfigData>().await()
            }
        }
    }
}