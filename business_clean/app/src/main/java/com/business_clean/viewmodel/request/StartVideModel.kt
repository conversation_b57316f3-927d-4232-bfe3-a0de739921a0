package com.business_clean.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.business_clean.app.network.NetUrl
import com.business_clean.data.initconfig.MapInfo
import com.business_clean.data.initconfig.TimestampEntity
import com.business_clean.data.mode.camera.CameraConfigData
import me.hgj.mvvmhelper.base.BaseViewModel
import me.hgj.mvvmhelper.ext.rxHttpRequest
import rxhttp.wrapper.param.RxHttp
import rxhttp.wrapper.param.toResponse

/**
 * 启动界面获取
 */
class StartVideModel : BaseViewModel() {

    ///map的info
    var mapInfo = MutableLiveData<MapInfo>()

    var timestampEntity = MutableLiveData<TimestampEntity>()

    var errorEntity = MutableLiveData<Any>()
    var errorMapEntity = MutableLiveData<Any>()

    var workRulesErrorEntity = MutableLiveData<Any>()


    //获取个人的方案配置
    var cameraConfigData = MutableLiveData<CameraConfigData>()

    /**
     * 拉取服务器时间 单独设置超时时间
     */
    fun requestTime() {
        rxHttpRequest {
            onRequest = {
                timestampEntity.value = RxHttp
                    .get(NetUrl.GET_TIME_STAMP)
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<TimestampEntity>().await()
            }
            onError = {
                errorEntity.value = true
            }
        }
    }


    /**
     * 动态拉取高德地图服务的Key
     */
    fun requestAMapInfo() {
        rxHttpRequest {
            onRequest = {
                mapInfo.value = RxHttp
                    .get(NetUrl.GET_A_MAP_KEY)
                    .add("map_code", "gao_de")
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<MapInfo>().await()
            }
            onError = {
                errorMapEntity.value = true
            }
        }
    }

    /**
     * 获取个人配置 CameraConfigData
     */
    fun requestUserWorkRules() {
        rxHttpRequest {
            onRequest = {
                cameraConfigData.value = RxHttp.get(NetUrl.GET_USER_ONE_CAMERA)
                    .connectTimeout(3000)
                    .writeTimeout(3000)
                    .readTimeout(3000)
                    .toResponse<CameraConfigData>().await()
            }
            onError = {
                workRulesErrorEntity.value = true
            }
        }
    }
}