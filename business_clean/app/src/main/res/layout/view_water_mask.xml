<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="238dp"
    android:layout_height="wrap_content"
    android:background="#30000000"
    android:orientation="vertical"
    android:paddingLeft="6dp"
    android:paddingRight="6dp">

    <TextView
        android:id="@+id/tv_mask_hour"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00"
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_40"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_mask_date"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="4dp"
        android:layout_marginTop="10dp"
        android:layout_toRightOf="@+id/tv_mask_hour"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_14"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/tv_mask_week"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_mask_date"
        android:layout_marginLeft="4dp"
        android:layout_toRightOf="@+id/tv_mask_hour"
        android:text=""
        android:textColor="@color/white"
        android:textSize="@dimen/font_size_14"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_mask_hour"
        android:orientation="horizontal"
        android:paddingBottom="6dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:layout_marginRight="2dp"
                    android:src="@mipmap/icon_camera_address" />

                <TextView
                    android:id="@+id/tv_mask_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="2dp"
                    android:text="无定位地址"
                    android:textColor="@color/white"
                    android:textSize="@dimen/font_size_12"
                    android:textStyle="bold" />

            </LinearLayout>


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:drawableStart="@mipmap/icon_camera_safe"
                android:drawablePadding="2dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:singleLine="true"
                android:text="熊猫清洁云已验证照片真实性"
                android:textColor="@color/base_primary_un_select"
                android:textSize="@dimen/font_size_12" />
        </LinearLayout>

    </LinearLayout>

</RelativeLayout>